package supie.common.flow.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import supie.common.core.annotation.MyDataSourceResolver;
import supie.common.core.base.dao.BaseDaoMapper;
import supie.common.core.base.service.BaseService;
import supie.common.core.constant.ApplicationConstant;
import supie.common.core.util.DefaultDataSourceResolver;
import supie.common.flow.dao.FlowVariableLogMapper;
import supie.common.flow.model.FlowVariableLog;
import supie.common.flow.service.FlowVariableLogService;
import supie.common.sequence.wrapper.IdGeneratorWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@MyDataSourceResolver(
        resolver = DefaultDataSourceResolver.class,
        intArg = ApplicationConstant.COMMON_FLOW_AND_ONLINE_DATASOURCE_TYPE)
@Service("flowVariableLogService")
public class FlowVariableLogServiceImpl extends BaseService<FlowVariableLog, Long> implements FlowVariableLogService {

    @Autowired
    private FlowVariableLogMapper flowVariableLogMapper;
    @Autowired
    private IdGeneratorWrapper idGenerator;

    @Override
    protected BaseDaoMapper<FlowVariableLog> mapper() {
        return flowVariableLogMapper;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveNew(FlowVariableLog o) {
        o.setId(idGenerator.nextLongId());
        o.setCreateTime(new Date());
        flowVariableLogMapper.insert(o);
    }

    @Override
    public Map<String, String> getVariableMap(String processInstanceId, List<String> taskKeys) {
        LambdaQueryWrapper<FlowVariableLog> qw = new LambdaQueryWrapper<>();
        qw.eq(FlowVariableLog::getProcessInstanceId, processInstanceId);
        qw.in(FlowVariableLog::getTaskKey, taskKeys);
        qw.orderByDesc(FlowVariableLog::getId);
        List<FlowVariableLog> dataList = flowVariableLogMapper.selectList(qw);
        return dataList.stream().collect(Collectors.toMap(
                FlowVariableLog::getTaskKey, FlowVariableLog::getVariableData, (existOne, newOne) -> existOne));
    }
}
