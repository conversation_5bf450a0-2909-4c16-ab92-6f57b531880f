package supie.common.flow.cmd;

import lombok.AllArgsConstructor;
import org.flowable.common.engine.api.delegate.Expression;
import org.flowable.common.engine.api.variable.VariableContainer;
import org.flowable.common.engine.impl.el.VariableContainerWrapper;
import org.flowable.common.engine.impl.interceptor.Command;
import org.flowable.common.engine.impl.interceptor.CommandContext;
import org.flowable.engine.impl.cfg.ProcessEngineConfigurationImpl;
import org.flowable.engine.impl.util.CommandContextUtil;

import java.util.Map;

/**
 * EL表达式解析和执行的命令。
 *
 * <AUTHOR> -rf .bug
 * @date 2025-05-16
 */
@AllArgsConstructor
public class ExpressionCmd implements Command<Object> {

    private String executionId;
    private String exp;

    @Override
    public Object execute(CommandContext commandContext) {
        ProcessEngineConfigurationImpl cfg = CommandContextUtil.getProcessEngineConfiguration(commandContext);
        Expression expression = cfg.getExpressionManager().createExpression(exp);
        Map<String, Object> variables = cfg.getRuntimeService().getVariables(executionId);
        VariableContainer variableContainer = new VariableContainerWrapper(variables);
        return cfg.getManagementService().executeCommand(cc -> expression.getValue(variableContainer));
    }
}
