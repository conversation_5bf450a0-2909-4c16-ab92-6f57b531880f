package supie.common.flow.listener;

import cn.hutool.core.util.ObjectUtil;
import supie.common.core.util.ApplicationContextHolder;
import supie.common.flow.constant.FlowConstant;
import lombok.extern.slf4j.Slf4j;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.delegate.TaskListener;
import org.flowable.task.service.delegate.DelegateTask;

import java.util.Map;

/**
 * 任务驳回后重新提交到该任务的通知监听器。
 *
 * <AUTHOR> -rf .bug
 * @date 2025-05-16
 */
@Slf4j
public class FlowTaskRejectBackListener implements TaskListener {

    private final transient RuntimeService runtimeService =
            ApplicationContextHolder.getBean(RuntimeService.class);

    @Override
    public void notify(DelegateTask delegateTask) {
        Map<String, Object> variables = delegateTask.getVariables();
        Object v = variables.get(FlowConstant.REJECT_BACK_TO_SOURCE_DATA_VAR);
        if (ObjectUtil.isNotEmpty(v)) {
            delegateTask.setAssignee(v.toString());
            runtimeService.removeVariableLocal(delegateTask.getExecutionId(), FlowConstant.REJECT_BACK_TO_SOURCE_DATA_VAR);
        }
    }
}
