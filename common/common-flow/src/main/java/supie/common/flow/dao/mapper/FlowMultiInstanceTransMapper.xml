<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="supie.common.flow.dao.FlowMultiInstanceTransMapper">
    <resultMap id="BaseResultMap" type="supie.common.flow.model.FlowMultiInstanceTrans">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="process_instance_id" jdbcType="VARCHAR" property="processInstanceId"/>
        <result column="task_id" jdbcType="VARCHAR" property="taskId"/>
        <result column="task_key" jdbcType="VARCHAR" property="taskKey"/>
        <result column="multi_instance_exec_id" jdbcType="VARCHAR" property="multiInstanceExecId"/>
        <result column="execution_id" jdbcType="VARCHAR" property="executionId"/>
        <result column="assignee_list" jdbcType="LONGVARCHAR" property="assigneeList"/>
        <result column="create_user_id" jdbcType="BIGINT" property="createUserId"/>
        <result column="create_login_name" jdbcType="VARCHAR" property="createLoginName"/>
        <result column="create_username" jdbcType="VARCHAR" property="createUsername"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>
</mapper>
