package supie.common.flow.service;

import supie.common.core.base.service.IBaseService;
import supie.common.flow.model.FlowVariableLog;

import java.util.List;
import java.util.Map;

/**
 * 流程请求日志操作服务接口。
 *
 * <AUTHOR> -rf .bug
 * @date 2025-05-16
 */
public interface FlowVariableLogService extends IBaseService<FlowVariableLog, Long> {

    /**
     * 保存流程任务变量日志对象。
     *
     * @param flowVariableLog 流程任务变量日志对象。
     */
    void saveNew(FlowVariableLog flowVariableLog);

    /**
     * 获取指定流程实例和任务标识集合的变量日志。
     *
     * @param processInstanceId 流程实例Id。
     * @param taskKeys          流程任务标识集合。
     * @return 键是taskKey，值是对应的变量数据。
     */
    Map<String, String> getVariableMap(String processInstanceId, List<String> taskKeys);
}
