<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="supie.common.flow.dao.FlowEntryVariableMapper">
    <resultMap id="BaseResultMap" type="supie.common.flow.model.FlowEntryVariable">
        <id column="variable_id" jdbcType="BIGINT" property="variableId"/>
        <result column="entry_id" jdbcType="BIGINT" property="entryId"/>
        <result column="variable_name" jdbcType="VARCHAR" property="variableName"/>
        <result column="show_name" jdbcType="VARCHAR" property="showName"/>
        <result column="variable_type" jdbcType="INTEGER" property="variableType"/>
        <result column="bind_datasource_id" jdbcType="BIGINT" property="bindDatasourceId"/>
        <result column="bind_relation_id" jdbcType="BIGINT" property="bindRelationId"/>
        <result column="bind_column_id" jdbcType="BIGINT" property="bindColumnId"/>
        <result column="builtin" jdbcType="BOOLEAN" property="builtin"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>

    <!-- 如果有逻辑删除字段过滤，请写到这里 -->
    <sql id="filterRef">
        <!-- 这里必须加上全包名，否则当filterRef被其他Mapper.xml包含引用的时候，就会调用Mapper.xml中的该SQL片段 -->
        <include refid="supie.common.flow.dao.FlowEntryVariableMapper.inputFilterRef"/>
    </sql>

    <!-- 这里仅包含调用接口输入的主表过滤条件 -->
    <sql id="inputFilterRef">
        <if test="flowEntryVariableFilter != null">
            <if test="flowEntryVariableFilter.entryId != null">
                AND zz_flow_entry_variable.entry_id = #{flowEntryVariableFilter.entryId}
            </if>
        </if>
    </sql>

    <select id="getFlowEntryVariableList" resultMap="BaseResultMap" parameterType="supie.common.flow.model.FlowEntryVariable">
        SELECT * FROM zz_flow_entry_variable
        <where>
            <include refid="filterRef"/>
        </where>
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>
</mapper>
