<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="supie.common.flow.dao.FlowWorkOrderExtMapper">
    <resultMap id="BaseResultMap" type="supie.common.flow.model.FlowWorkOrderExt">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="work_order_id" jdbcType="BIGINT" property="workOrderId"/>
        <result column="draft_data" jdbcType="LONGVARCHAR" property="draftData"/>
        <result column="business_data" jdbcType="LONGVARCHAR" property="businessData"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="update_user_id" jdbcType="BIGINT" property="updateUserId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="create_user_id" jdbcType="BIGINT" property="createUserId"/>
        <result column="deleted_flag" jdbcType="INTEGER" property="deletedFlag"/>
    </resultMap>
</mapper>
