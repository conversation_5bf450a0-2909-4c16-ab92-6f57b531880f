package supie.common.flow.online.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import supie.common.core.annotation.MyDataSource;
import supie.common.core.constant.ApplicationConstant;
import supie.common.flow.base.service.BaseFlowOnlineService;
import supie.common.flow.model.FlowWorkOrder;
import supie.common.flow.service.FlowWorkOrderService;
import supie.common.flow.util.FlowCustomExtFactory;
import supie.common.flow.online.object.TransactionalFlowBusinessData;
import supie.common.online.exception.OnlineRuntimeException;
import supie.common.online.model.OnlineColumn;
import supie.common.online.model.OnlineTable;
import supie.common.online.model.OnlineDatasource;
import supie.common.online.model.OnlineDatasourceRelation;
import supie.common.online.model.constant.FieldKind;
import supie.common.online.service.OnlineDatasourceRelationService;
import supie.common.online.service.OnlineDatasourceService;
import supie.common.online.service.OnlineOperationService;
import supie.common.online.service.OnlineTableService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.PostConstruct;
import java.util.*;

/**
 * 在线表单和流程监听器进行数据对接时的服务实现类。
 *
 * <AUTHOR> -rf .bug
 * @date 2025-05-16
 */
@Slf4j
@MyDataSource(ApplicationConstant.COMMON_FLOW_AND_ONLINE_DATASOURCE_TYPE)
@Service("flowOnlineBusinessService")
public class FlowOnlineBusinessServiceImpl implements BaseFlowOnlineService {

    @Autowired
    private FlowCustomExtFactory flowCustomExtFactory;
    @Autowired
    private FlowWorkOrderService flowWorkOrderService;
    @Autowired
    private OnlineTableService onlineTableService;
    @Autowired
    private OnlineDatasourceService onlineDatasourceService;
    @Autowired
    private OnlineDatasourceRelationService onlineDatasourceRelationService;
    @Autowired
    private OnlineOperationService onlineOperationService;

    @PostConstruct
    public void doRegister() {
        flowCustomExtFactory.getOnlineBusinessDataExtHelper().setOnlineBusinessService(this);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateFlowStatus(FlowWorkOrder workOrder) {
        OnlineTable onlineTable = onlineTableService.getOnlineTableFromCache(workOrder.getOnlineTableId());
        if (onlineTable == null) {
            log.error("OnlineTableId [{}] doesn't exist while calling FlowOnlineBusinessServiceImpl.updateFlowStatus",
                    workOrder.getOnlineTableId());
            return;
        }
        String dataId = workOrder.getBusinessKey();
        this.handleTransactionalFlowBusinessData(workOrder, "FlowOnlineBusinessServiceImpl.updateFlowStatus");
        for (OnlineColumn column : onlineTable.getColumnMap().values()) {
            if (ObjectUtil.equals(column.getFieldKind(), FieldKind.FLOW_FINISHED_STATUS)) {
                onlineOperationService.updateColumn(onlineTable, dataId, column, workOrder.getFlowStatus());
            }
            if (ObjectUtil.equals(column.getFieldKind(), FieldKind.FLOW_APPROVAL_STATUS)) {
                onlineOperationService.updateColumn(onlineTable, dataId, column, workOrder.getLatestApprovalStatus());
            }
        }
    }

    @Override
    public void deleteBusinessData(FlowWorkOrder workOrder) {
        OnlineTable onlineTable = onlineTableService.getOnlineTableFromCache(workOrder.getOnlineTableId());
        if (onlineTable == null) {
            log.error("OnlineTableId [{}] doesn't exist while calling FlowOnlineBusinessServiceImpl.deleteBusinessData",
                    workOrder.getOnlineTableId());
            return;
        }
        OnlineDatasource datasource =
                onlineDatasourceService.getOnlineDatasourceByMasterTableId(onlineTable.getTableId());
        List<OnlineDatasourceRelation> relationList =
                onlineDatasourceRelationService.getOnlineDatasourceRelationListFromCache(CollUtil.newHashSet(datasource.getDatasourceId()));
        String dataId = workOrder.getBusinessKey();
        for (OnlineDatasourceRelation relation : relationList) {
            OnlineTable slaveTable = onlineTableService.getOnlineTableFromCache(relation.getSlaveTableId());
            if (slaveTable == null) {
                throw new OnlineRuntimeException("数据验证失败，数据源关联 [" + relation.getRelationName() + "] 的从表Id不存在！");
            }
            relation.setSlaveTable(slaveTable);
        }
        this.handleTransactionalFlowBusinessData(workOrder, "FlowOnlineBusinessServiceImpl.deleteBusinessData");
        onlineOperationService.delete(onlineTable, relationList, dataId);
    }

    @Override
    public void updateBusinessData(String processInstanceId, String businessKey, JSONObject masterData) {
        OnlineTable table = this.getOnlineTableByProcessInstanceId(processInstanceId);
        onlineOperationService.update(table, masterData);
    }

    @Override
    public String getBusinessData(String processInstanceId, String businessKey) {
        OnlineTable table = this.getOnlineTableByProcessInstanceId(processInstanceId);
        return JSON.toJSONString(onlineOperationService.getMasterData(table, null, null, businessKey));
    }

    @Override
    public List<Map<String, Object>> executeQuery(Long dblinkId, String sql) {
        return onlineOperationService.executeQuery(dblinkId, sql);
    }

    @Override
    public void executeSql(Long dblinkId, String sql) {
        onlineOperationService.executeSql(dblinkId, sql);
    }

    private OnlineTable getOnlineTableByProcessInstanceId(String processInstanceId) {
        FlowWorkOrder flowWorkOrder = flowWorkOrderService.getFlowWorkOrderByProcessInstanceId(processInstanceId);
        return onlineTableService.getOnlineTableFromCache(flowWorkOrder.getOnlineTableId());
    }

    private void handleTransactionalFlowBusinessData(FlowWorkOrder workOrder, String desc) {
        TransactionalFlowBusinessData eventData = TransactionalFlowBusinessData.getFromRequestAttribute();
        if (eventData != null) {
            eventData.setProcessInstanceId(workOrder.getProcessInstanceId());
            eventData.setOperationDesc(desc);
        }
    }
}
