package supie.common.online.util;

import supie.common.core.exception.MyRuntimeException;
import supie.common.dbutil.provider.DataSourceProvider;
import supie.common.dbutil.util.DataSourceUtil;
import supie.common.online.model.OnlineDblink;
import supie.common.online.service.OnlineDblinkService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 在线表单模块动态加载的数据源工具类。
 *
 * <AUTHOR> -rf .bug
 * @date 2025-05-16
 */
@Slf4j
@Component
public class OnlineDataSourceUtil extends DataSourceUtil {

    @Autowired
    private OnlineDblinkService dblinkService;

    @Override
    public int getDblinkTypeByDblinkId(Long dblinkId) {
        DataSourceProvider provider = this.dblinkProviderMap.get(dblinkId);
        if (provider != null) {
            return provider.getDblinkType();
        }
        OnlineDblink dblink = dblinkService.getById(dblinkId);
        if (dblink == null) {
            throw new MyRuntimeException("Online DblinkId [" + dblinkId + "] doesn't exist!");
        }
        this.dblinkProviderMap.put(dblinkId, this.getProvider(dblink.getDblinkType()));
        return dblink.getDblinkType();
    }

    @Override
    public String getDblinkConfigurationByDblinkId(Long dblinkId) {
        OnlineDblink dblink = dblinkService.getById(dblinkId);
        if (dblink == null) {
            throw new MyRuntimeException("Online DblinkId [" + dblinkId + "] doesn't exist!");
        }
        return dblink.getConfiguration();
    }
}
