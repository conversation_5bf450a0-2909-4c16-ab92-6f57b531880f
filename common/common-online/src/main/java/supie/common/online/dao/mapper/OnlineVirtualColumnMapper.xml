<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="supie.common.online.dao.OnlineVirtualColumnMapper">
    <resultMap id="BaseResultMap" type="supie.common.online.model.OnlineVirtualColumn">
        <id column="virtual_column_id" jdbcType="BIGINT" property="virtualColumnId"/>
        <result column="table_id" jdbcType="BIGINT" property="tableId"/>
        <result column="object_field_name" jdbcType="VARCHAR" property="objectFieldName"/>
        <result column="object_field_type" jdbcType="VARCHAR" property="objectFieldType"/>
        <result column="column_prompt" jdbcType="VARCHAR" property="columnPrompt"/>
        <result column="virtual_type" jdbcType="INTEGER" property="virtualType"/>
        <result column="datasource_id" jdbcType="BIGINT" property="datasourceId"/>
        <result column="relation_id" jdbcType="BIGINT" property="relationId"/>
        <result column="aggregation_table_id" jdbcType="BIGINT" property="aggregationTableId"/>
        <result column="aggregation_column_id" jdbcType="BIGINT" property="aggregationColumnId"/>
        <result column="aggregation_type" jdbcType="INTEGER" property="aggregationType"/>
        <result column="where_clause_json" jdbcType="VARCHAR" property="whereClauseJson"/>
    </resultMap>

    <!-- 如果有逻辑删除字段过滤，请写到这里 -->
    <sql id="filterRef">
        <!-- 这里必须加上全包名，否则当filterRef被其他Mapper.xml包含引用的时候，就会调用Mapper.xml中的该SQL片段 -->
        <include refid="supie.common.online.dao.OnlineVirtualColumnMapper.inputFilterRef"/>
    </sql>

    <!-- 这里仅包含调用接口输入的主表过滤条件 -->
    <sql id="inputFilterRef">
        <if test="onlineVirtualColumnFilter != null">
            <if test="onlineVirtualColumnFilter.datasourceId != null">
                AND zz_online_virtual_column.datasource_id = #{onlineVirtualColumnFilter.datasourceId}
            </if>
            <if test="onlineVirtualColumnFilter.relationId != null">
                AND zz_online_virtual_column.relation_id = #{onlineVirtualColumnFilter.relationId}
            </if>
            <if test="onlineVirtualColumnFilter.tableId != null">
                AND zz_online_virtual_column.table_id = #{onlineVirtualColumnFilter.tableId}
            </if>
            <if test="onlineVirtualColumnFilter.aggregationColumnId != null">
                AND zz_online_virtual_column.aggregation_column_id = #{onlineVirtualColumnFilter.aggregationColumnId}
            </if>
            <if test="onlineVirtualColumnFilter.virtualType != null">
                AND zz_online_virtual_column.virtual_type = #{onlineVirtualColumnFilter.virtualType}
            </if>
        </if>
    </sql>

    <select id="getOnlineVirtualColumnList" resultMap="BaseResultMap"
            parameterType="supie.common.online.model.OnlineVirtualColumn">
        SELECT * FROM zz_online_virtual_column
        <where>
            <include refid="filterRef"/>
        </where>
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>
</mapper>
