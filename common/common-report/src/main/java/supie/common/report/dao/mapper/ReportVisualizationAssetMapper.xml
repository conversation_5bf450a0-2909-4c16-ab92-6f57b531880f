<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="supie.common.report.dao.ReportVisualizationAssetMapper">
    <resultMap id="BaseResultMap" type="supie.common.report.model.ReportVisualizationAsset">
        <id column="asset_id" jdbcType="BIGINT" property="assetId"/>
        <result column="visual_id" jdbcType="BIGINT" property="visualId"/>
        <result column="asset_name" jdbcType="VARCHAR" property="assetName"/>
        <result column="thumbnail_img" jdbcType="LONGVARCHAR" property="thumbnailImg"/>
        <result column="asset_img" jdbcType="LONGVARCHAR" property="assetImg"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="create_user_id" jdbcType="BIGINT" property="createUserId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="update_user_id" jdbcType="BIGINT" property="updateUserId"/>
    </resultMap>
</mapper>
