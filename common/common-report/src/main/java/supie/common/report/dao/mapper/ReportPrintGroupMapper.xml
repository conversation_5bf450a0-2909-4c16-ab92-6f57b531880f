<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="supie.common.report.dao.ReportPrintGroupMapper">
    <resultMap id="BaseResultMap" type="supie.common.report.model.ReportPrintGroup">
        <id column="group_id" jdbcType="BIGINT" property="groupId"/>
        <result column="tenant_id" jdbcType="BIGINT" property="tenantId"/>
        <result column="app_code" jdbcType="VARCHAR" property="appCode"/>
        <result column="group_name" jdbcType="VARCHAR" property="groupName"/>
        <result column="parent_id" jdbcType="BIGINT" property="parentId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="create_user_id" jdbcType="BIGINT" property="createUserId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="update_user_id" jdbcType="BIGINT" property="updateUserId"/>
    </resultMap>

    <select id="getReportPrintGroupList" resultMap="BaseResultMap"
            parameterType="supie.common.report.model.ReportPrintGroup">
        SELECT * FROM zz_report_print_group
        <where>
            <if test="reportPrintGroupFilter.tenantId == null">
                AND zz_report_print_group.tenant_id IS NULL
            </if>
            <if test="reportPrintGroupFilter.tenantId != null">
                AND zz_report_print_group.tenant_id = #{reportPrintGroupFilter.tenantId}
            </if>
            <if test="reportPrintGroupFilter.appCode == null">
                AND zz_report_print_group.app_code IS NULL
            </if>
            <if test="reportPrintGroupFilter.appCode != null">
                AND zz_report_print_group.app_code = #{reportPrintGroupFilter.appCode}
            </if>
        </where>
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>
</mapper>
