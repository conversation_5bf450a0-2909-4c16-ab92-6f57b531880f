package supie.common.dbutil.provider;

import com.alibaba.fastjson.JSON;
import supie.common.dbutil.constant.DblinkType;

/**
 * Doris数据源的提供者实现类。
 *
 * <AUTHOR> -rf .bug
 * @date 2025-05-16
 */
public class Doris<PERSON>rovider extends MySqlProvider {

    @Override
    public int getDblinkType() {
        return DblinkType.DORIS;
    }

    @Override
    public JdbcConfig getJdbcConfig(String configuration) {
        return JSON.parseObject(configuration, DorisConfig.class);
    }
}