# 华为昇腾910A显卡支持说明

## 修改概述

本次修改为SSH工具类添加了对华为昇腾910A显卡的支持，同时保持对NVIDIA GPU的兼容性。

## 主要修改内容

### 1. 脚本文件更新 (`system_info_check.sh`)

#### GPU检测优先级调整
```bash
# 新的检测优先级：
1. npu-smi (华为昇腾NPU)
2. nvidia-smi (NVIDIA GPU) 
3. /proc/driver/nvidia (NVIDIA驱动检测)
4. /sys/class/accel (华为昇腾设备文件检测)
```

#### 华为昇腾910A命令支持
- **NPU数量检测**: `npu-smi info | grep -c "NPU"`
- **NPU内存检测**: 解析 `npu-smi info` 输出中的内存信息
- **NPU详细信息**: 提取每个NPU的索引、名称、总内存、已用内存、空闲内存

### 2. SshUtil.java 更新

#### 废弃方法中的命令更新
所有标记为 `@Deprecated` 的方法都已更新以支持华为昇腾910A：

- `getSystemInfoCommands()` - 更新GPU检测命令
- `getSystemInfoScript()` - 更新内联脚本
- `buildInlineSystemInfoCommand()` - 更新内联命令构建

## 华为昇腾910A检测机制

### 1. NPU数量检测
```bash
if command -v npu-smi >/dev/null 2>&1; then
    npu-smi info 2>/dev/null | grep -c "NPU"
elif [ -d /sys/class/accel ]; then
    ls /sys/class/accel/ 2>/dev/null | wc -l
fi
```

### 2. NPU内存检测
```bash
npu-smi info 2>/dev/null | grep -i "memory" | awk '
BEGIN { total = 0 }
/Total.*Memory/ { 
    # 支持GB和MB单位自动转换
    if ($0 ~ /GB/) {
        gsub(/[^0-9.]/, "", $NF)
        total += $NF
    } else if ($0 ~ /MB/) {
        gsub(/[^0-9.]/, "", $NF)
        total += $NF / 1024
    }
}
END { 
    if (total > 0) {
        printf "%.1fGB", total
    } else {
        print "0GB"
    }
}'
```

### 3. NPU详细信息解析
脚本能够解析 `npu-smi info` 输出，提取以下信息：
- NPU索引 (NPU0, NPU1, ...)
- 芯片名称 (默认为 "Ascend 910A")
- 总内存、已用内存、空闲内存

## 兼容性保证

### 向后兼容
- 保留所有NVIDIA GPU检测逻辑
- 华为昇腾检测优先，NVIDIA检测作为备选
- 所有原有功能保持不变

### 多厂商支持
```bash
# 检测优先级
1. 华为昇腾 (npu-smi)
2. NVIDIA (nvidia-smi)
3. 系统文件检测 (/sys/class/accel, /proc/driver/nvidia)
```

## 返回数据格式

### 华为昇腾910A示例
```json
{
  "connected": true,
  "systemInfo": "Linux",
  "systemVersion": "Ubuntu 20.04.3 LTS",
  "cpuCoreCount": "64",
  "totalMemory": "256.0GB",
  "usedMemory": "128.5GB",
  "availableMemory": "127.5GB",
  "gpuCount": "8",
  "gpuMemory": "256.0GB",
  "gpuDetails": "[
    {
      \"index\": \"0\",
      \"name\": \"Ascend 910A\",
      \"totalMemory\": \"32.0GB\",
      \"usedMemory\": \"8.5GB\",
      \"freeMemory\": \"23.5GB\"
    },
    {
      \"index\": \"1\",
      \"name\": \"Ascend 910A\",
      \"totalMemory\": \"32.0GB\",
      \"usedMemory\": \"12.1GB\",
      \"freeMemory\": \"19.9GB\"
    }
  ]",
  "message": "连接测试成功"
}
```

## 华为昇腾910A特性

### 技术规格
- **内存**: 通常为32GB HBM2内存
- **计算能力**: 支持FP16/INT8等多种精度
- **接口**: PCIe 4.0接口
- **功耗**: 约310W

### 检测命令
- **npu-smi**: 华为昇腾NPU管理工具
- **系统路径**: `/sys/class/accel/` 设备文件

## 使用说明

### 环境要求
1. **华为昇腾驱动**: 需要安装华为昇腾驱动程序
2. **npu-smi工具**: 需要安装npu-smi管理工具
3. **系统权限**: 需要有读取设备信息的权限

### 测试验证
```bash
# 检查npu-smi是否可用
command -v npu-smi

# 查看NPU信息
npu-smi info

# 检查设备文件
ls /sys/class/accel/
```

## 故障排除

### 常见问题

1. **npu-smi命令不存在**
   - 检查华为昇腾驱动是否正确安装
   - 确认npu-smi工具是否在PATH中

2. **权限不足**
   - 确保用户有访问 `/sys/class/accel/` 的权限
   - 检查npu-smi命令的执行权限

3. **内存信息解析失败**
   - 检查npu-smi输出格式是否符合预期
   - 验证awk脚本的正则表达式匹配

### 调试方法
```bash
# 手动执行检测命令
npu-smi info
ls /sys/class/accel/

# 检查脚本输出
bash /tmp/system_info_check.sh
```

## 注意事项

1. **命令优先级**: 华为昇腾检测优先于NVIDIA检测
2. **内存单位**: 自动处理GB和MB单位转换
3. **错误处理**: 所有命令都有错误处理和默认值
4. **日志记录**: 详细记录检测过程和结果
5. **性能影响**: 检测命令执行时间较短，不影响整体性能

## 更新历史

- **2025-01-20**: 添加华为昇腾910A支持
- 保持NVIDIA GPU兼容性
- 更新所有相关检测脚本和命令
- 添加详细的错误处理和日志记录 