package supie.webadmin.app.util;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

/**
 * NginxConfigDiagnostic测试类
 */
class NginxConfigDiagnosticTest {

    @Mock
    private SshConnectionUtil sshConnectionUtil;

    @InjectMocks
    private NginxConfigDiagnostic nginxConfigDiagnostic;

    @TempDir
    Path tempDir;

    private Path nginxConfigFile;

    @BeforeEach
    void setUp() throws IOException {
        MockitoAnnotations.openMocks(this);
        
        // 创建临时Nginx配置文件
        nginxConfigFile = tempDir.resolve("nginx.conf");
        
        // 设置配置文件路径
        ReflectionTestUtils.setField(nginxConfigDiagnostic, "nginxConfig", nginxConfigFile.toString());
        
        // 创建测试配置内容
        createTestNginxConfig();
    }

    private void createTestNginxConfig() throws IOException {
        String configContent = """
                user  nginx;
                worker_processes  auto;
                
                events {
                    worker_connections  1024;
                }
                
                http {
                    server {
                        listen       80;
                        server_name  _;
                        
                        location ~ ^/code1935803844944465920/ {
                            proxy_pass http://code-1935803844944465920:8080;
                            rewrite ^/code1935803844944465920/(.*)$ /$1 break;
                        }
                        
                        location ~ ^/code1935804000000000000/ {
                            proxy_pass http://code-1935804000000000000:9000;
                            rewrite ^/code1935804000000000000/(.*)$ /$1 break;
                        }
                        
                        location ~ ^/code1935805000000000000/ {
                            proxy_pass http://code-1935805000000000000:8080;
                            rewrite ^/code1935805000000000000/(.*)$ /$1 break;
                        }
                    }
                }
                """;
        
        Files.write(nginxConfigFile, configContent.getBytes());
    }

    @Test
    void testDiagnoseNginxConfig_NoIssues() {
        // 测试没有问题的配置
        List<String> issues = nginxConfigDiagnostic.diagnoseNginxConfig();
        
        // 应该没有语法错误
        assertTrue(issues.stream().noneMatch(issue -> issue.contains("大括号不匹配")));
    }

    @Test
    void testDiagnoseNginxConfig_FileNotExists() {
        // 设置不存在的文件路径
        ReflectionTestUtils.setField(nginxConfigDiagnostic, "nginxConfig", "/nonexistent/nginx.conf");
        
        List<String> issues = nginxConfigDiagnostic.diagnoseNginxConfig();
        
        assertFalse(issues.isEmpty());
        assertTrue(issues.get(0).contains("Nginx配置文件不存在"));
    }

    @Test
    void testCleanupInvalidLocations() {
        // 模拟运行中的容器列表（只包含部分容器）
        List<String> runningContainers = Arrays.asList(
                "code-1935803844944465920",
                "code-1935805000000000000"
        );
        
        int removedCount = nginxConfigDiagnostic.cleanupInvalidLocations(runningContainers);
        
        // 应该移除1个无效的location块（code-1935804000000000000不在运行列表中）
        assertEquals(1, removedCount);
        
        // 验证配置文件内容
        try {
            String updatedConfig = Files.readString(nginxConfigFile);
            assertFalse(updatedConfig.contains("code-1935804000000000000"));
            assertTrue(updatedConfig.contains("code-1935803844944465920"));
            assertTrue(updatedConfig.contains("code-1935805000000000000"));
        } catch (IOException e) {
            fail("Failed to read updated config file");
        }
    }

    @Test
    void testCleanupInvalidLocations_NoRunningContainers() {
        List<String> runningContainers = Arrays.asList();
        
        int removedCount = nginxConfigDiagnostic.cleanupInvalidLocations(runningContainers);
        
        // 应该移除所有的location块
        assertEquals(3, removedCount);
    }

    @Test
    void testGetRunningContainers() throws Exception {
        // 模拟SSH命令执行结果
        String dockerPsOutput = """
                code-1935803844944465920
                code-1935804000000000000
                code-1935805000000000000
                """;
        
        when(sshConnectionUtil.executeCommand(any(), anyString()))
                .thenReturn(dockerPsOutput);
        
        SshConnection mockConnection = new SshConnection();
        List<String> containers = nginxConfigDiagnostic.getRunningContainers(mockConnection);
        
        assertEquals(3, containers.size());
        assertTrue(containers.contains("code-1935803844944465920"));
        assertTrue(containers.contains("code-1935804000000000000"));
        assertTrue(containers.contains("code-1935805000000000000"));
    }

    @Test
    void testGetRunningContainers_NoContainers() throws Exception {
        // 模拟没有容器的情况
        when(sshConnectionUtil.executeCommand(any(), anyString()))
                .thenReturn("");
        
        SshConnection mockConnection = new SshConnection();
        List<String> containers = nginxConfigDiagnostic.getRunningContainers(mockConnection);
        
        assertTrue(containers.isEmpty());
    }

    @Test
    void testFormatNginxConfig() throws IOException {
        // 创建格式不规范的配置
        String unformattedConfig = """
                user nginx;
                worker_processes auto;
                events {
                worker_connections 1024;
                }
                http {
                server {
                listen 80;
                location / {
                return 404;
                }
                }
                }
                """;
        
        Files.write(nginxConfigFile, unformattedConfig.getBytes());
        
        boolean result = nginxConfigDiagnostic.formatNginxConfig();
        
        assertTrue(result);
        
        // 验证格式化后的内容
        String formattedConfig = Files.readString(nginxConfigFile);
        String[] lines = formattedConfig.split("\n");
        
        // 检查缩进是否正确
        boolean foundIndentedLine = false;
        for (String line : lines) {
            if (line.startsWith("    ") && line.trim().equals("worker_connections 1024;")) {
                foundIndentedLine = true;
                break;
            }
        }
        assertTrue(foundIndentedLine, "Configuration should be properly indented");
    }

    @Test
    void testDiagnoseBracesMismatch() throws IOException {
        // 创建大括号不匹配的配置
        String invalidConfig = """
                http {
                    server {
                        listen 80;
                    }
                    # 缺少一个右大括号
                """;
        
        Files.write(nginxConfigFile, invalidConfig.getBytes());
        
        List<String> issues = nginxConfigDiagnostic.diagnoseNginxConfig();
        
        assertTrue(issues.stream().anyMatch(issue -> issue.contains("大括号不匹配")));
    }

    @Test
    void testDiagnoseInvalidTaskId() throws IOException {
        // 创建包含无效任务ID的配置
        String configWithInvalidTaskId = """
                http {
                    server {
                        location ~ ^/codeinvalid/ {
                            proxy_pass http://code-invalid:8080;
                        }
                    }
                }
                """;
        
        Files.write(nginxConfigFile, configWithInvalidTaskId.getBytes());
        
        List<String> issues = nginxConfigDiagnostic.diagnoseNginxConfig();
        
        assertTrue(issues.stream().anyMatch(issue -> issue.contains("可能无效的任务ID格式")));
    }
}
