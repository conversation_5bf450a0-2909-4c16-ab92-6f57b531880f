package supie.webadmin;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableAsync;
import supie.webadmin.app.service.AppBusinessFileService;
import supie.webadmin.app.service.impl.AppBusinessFileServiceImpl;

/**
 * 应用服务启动类。
 *
 * <AUTHOR> -rf .bug
 * @date 2025-05-16
 */
@EnableAsync
@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class})
@ComponentScan("supie")
public class WebAdminApplication {

	public static void main(String[] args) {
		SpringApplication.run(WebAdminApplication.class, args);
	}

	@Bean("businessFileService")
	public AppBusinessFileService businessFileService() {
		return new AppBusinessFileServiceImpl();
	}
}
