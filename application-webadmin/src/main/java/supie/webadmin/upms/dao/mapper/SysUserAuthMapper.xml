<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="supie.webadmin.upms.dao.SysUserAuthMapper">
    <resultMap id="BaseResultMap" type="supie.webadmin.upms.model.SysUserAuth">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="source" jdbcType="VARCHAR" property="source"/>
        <result column="open_id" jdbcType="VARCHAR" property="openId"/>
        <result column="union_id" jdbcType="VARCHAR" property="unionId"/>
        <result column="auth_user_id" jdbcType="VARCHAR" property="authUserId"/>
        <result column="extra_data" jdbcType="VARCHAR" property="extraData"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>
</mapper>
