package supie.webadmin.app.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.github.pagehelper.Page;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import supie.common.core.annotation.MyDataSource;
import supie.common.core.base.dao.BaseDaoMapper;
import supie.common.core.base.service.BaseService;
import supie.common.core.constant.GlobalDeletedFlag;
import supie.common.core.object.MyRelationParam;
import supie.common.core.util.MyModelUtil;
import supie.common.sequence.wrapper.IdGeneratorWrapper;
import supie.webadmin.app.dao.AppBusinessFileMapper;
import supie.webadmin.app.model.BusinessFile;
import supie.webadmin.app.service.AppBusinessFileService;
import supie.webadmin.config.DataSourceType;

import java.util.List;

/**
 * 业务附件表数据操作服务类。
 *
 * <AUTHOR> -rf .bug
 * @date 2025-02-17
 */
@Slf4j
@MyDataSource(DataSourceType.MAIN)
@Service("appBusinessFileService")
public class AppBusinessFileServiceImpl extends BaseService<BusinessFile, Long> implements AppBusinessFileService {

    @Resource(name = "appBusinessFileMapper")
    private AppBusinessFileMapper businessFileMapper;
    @Autowired
    public IdGeneratorWrapper idGenerator;



    /**
     * 返回当前Service的主表Mapper对象。
     *
     * @return 主表Mapper对象。
     */
    @Override
    protected BaseDaoMapper<BusinessFile> mapper() {
        return businessFileMapper;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public BusinessFile saveNew(BusinessFile businessFile) {
        businessFileMapper.insert(this.buildDefaultValue(businessFile));
        return businessFile;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveNewBatch(List<BusinessFile> businessFileList) {
        if (CollUtil.isNotEmpty(businessFileList)) {
            businessFileList.forEach(this::buildDefaultValue);
            businessFileMapper.insertList(businessFileList);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean update(BusinessFile businessFile, BusinessFile originalBusinessFile) {
        MyModelUtil.fillCommonsForUpdate(businessFile, originalBusinessFile);
        // 这里重点提示，在执行主表数据更新之前，如果有哪些字段不支持修改操作，请用原有数据对象字段替换当前数据字段。
        UpdateWrapper<BusinessFile> uw = this.createUpdateQueryForNullValue(businessFile, businessFile.getId());
        return businessFileMapper.update(businessFile, uw) == 1;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean remove(Long id) {
        return businessFileMapper.deleteById(id) == 1;
    }



    @Override
    public List<BusinessFile> getBusinessFileListWithRelation(BusinessFile filter, String orderBy) {
        List<BusinessFile> resultList = null;
        try {
            log.info("对象状态---{}", ObjectUtil.isNull(businessFileMapper));
            resultList = businessFileMapper.getBusinessFileList(filter, orderBy);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        // 在缺省生成的代码中，如果查询结果resultList不是Page对象，说明没有分页，那么就很可能是数据导出接口调用了当前方法。
        // 为了避免一次性的大量数据关联，规避因此而造成的系统运行性能冲击，这里手动进行了分批次读取，开发者可按需修改该值。
        int batchSize = resultList instanceof Page ? 0 : 1000;
        this.buildRelationForDataList(resultList, MyRelationParam.normal(), batchSize);
        return resultList;
    }

    @Override
    public List<BusinessFile> getGroupedBusinessFileListWithRelation(
            BusinessFile filter, String groupSelect, String groupBy, String orderBy) {
        List<BusinessFile> resultList =
                businessFileMapper.getGroupedBusinessFileList(filter, groupSelect, groupBy, orderBy);
        // 在缺省生成的代码中，如果查询结果resultList不是Page对象，说明没有分页，那么就很可能是数据导出接口调用了当前方法。
        // 为了避免一次性的大量数据关联，规避因此而造成的系统运行性能冲击，这里手动进行了分批次读取，开发者可按需修改该值。
        int batchSize = resultList instanceof Page ? 0 : 1000;
        // NOTE: 这里只是包含了关联数据，聚合计算数据没有包含。
        // 主要原因是，由于聚合字段通常被视为普通字段使用，不会在group by的从句中出现，语义上也不会在此关联。
        this.buildRelationForDataList(resultList, MyRelationParam.normal(), batchSize);
        return resultList;
    }


    private BusinessFile buildDefaultValue(BusinessFile businessFile) {
        if (businessFile.getId() == null) {
            businessFile.setId(idGenerator.nextLongId());
        }
        MyModelUtil.fillCommonsForInsert(businessFile);
        businessFile.setIsDelete(GlobalDeletedFlag.NORMAL);
        return businessFile;
    }
}
