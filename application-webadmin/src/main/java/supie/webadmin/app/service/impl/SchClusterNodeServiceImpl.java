package supie.webadmin.app.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import supie.webadmin.app.service.*;
import supie.webadmin.app.dao.*;
import supie.webadmin.app.model.*;
import supie.common.core.base.dao.BaseDaoMapper;
import supie.common.core.constant.GlobalDeletedFlag;
import supie.common.core.object.TokenData;
import supie.common.core.object.MyRelationParam;
import supie.common.core.base.service.BaseService;
import supie.common.sequence.wrapper.IdGeneratorWrapper;
import com.github.pagehelper.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 集群节点表数据操作服务类。
 *
 * <AUTHOR> -rf .bug
 * @date 2025-05-16
 */
@Slf4j
@Service("schClusterNodeService")
public class SchClusterNodeServiceImpl extends BaseService<SchClusterNode, Long> implements SchClusterNodeService {

    @Autowired
    private IdGeneratorWrapper idGenerator;
    @Autowired
    private SchClusterNodeMapper schClusterNodeMapper;

    /**
     * 返回当前Service的主表Mapper对象。
     *
     * @return 主表Mapper对象。
     */
    @Override
    protected BaseDaoMapper<SchClusterNode> mapper() {
        return schClusterNodeMapper;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public SchClusterNode saveNew(SchClusterNode schClusterNode) {
        schClusterNodeMapper.insert(this.buildDefaultValue(schClusterNode));
        return schClusterNode;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveNewBatch(List<SchClusterNode> schClusterNodeList) {
        if (CollUtil.isNotEmpty(schClusterNodeList)) {
            schClusterNodeList.forEach(this::buildDefaultValue);
            schClusterNodeMapper.insertList(schClusterNodeList);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean update(SchClusterNode schClusterNode, SchClusterNode originalSchClusterNode) {
        schClusterNode.setCreateUserId(originalSchClusterNode.getCreateUserId());
        schClusterNode.setUpdateUserId(TokenData.takeFromRequest().getUserId());
        schClusterNode.setUpdateTime(new Date());
        schClusterNode.setCreateTime(originalSchClusterNode.getCreateTime());
        // 这里重点提示，在执行主表数据更新之前，如果有哪些字段不支持修改操作，请用原有数据对象字段替换当前数据字段。
        UpdateWrapper<SchClusterNode> uw = this.createUpdateQueryForNullValue(schClusterNode, schClusterNode.getId());
        return schClusterNodeMapper.update(schClusterNode, uw) == 1;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean remove(Long id) {
        return schClusterNodeMapper.deleteById(id) == 1;
    }

    @Override
    public List<SchClusterNode> getSchClusterNodeList(SchClusterNode filter, String orderBy) {
        return schClusterNodeMapper.getSchClusterNodeList(filter, orderBy);
    }

    @Override
    public List<SchClusterNode> getSchClusterNodeListWithRelation(SchClusterNode filter, String orderBy) {
        List<SchClusterNode> resultList = schClusterNodeMapper.getSchClusterNodeList(filter, orderBy);
        // 在缺省生成的代码中，如果查询结果resultList不是Page对象，说明没有分页，那么就很可能是数据导出接口调用了当前方法。
        // 为了避免一次性的大量数据关联，规避因此而造成的系统运行性能冲击，这里手动进行了分批次读取，开发者可按需修改该值。
        int batchSize = resultList instanceof Page ? 0 : 1000;
        this.buildRelationForDataList(resultList, MyRelationParam.normal(), batchSize);
        return resultList;
    }

    @Override
    public List<SchClusterNode> getGroupedSchClusterNodeListWithRelation(
            SchClusterNode filter, String groupSelect, String groupBy, String orderBy) {
        List<SchClusterNode> resultList =
                schClusterNodeMapper.getGroupedSchClusterNodeList(filter, groupSelect, groupBy, orderBy);
        // 在缺省生成的代码中，如果查询结果resultList不是Page对象，说明没有分页，那么就很可能是数据导出接口调用了当前方法。
        // 为了避免一次性的大量数据关联，规避因此而造成的系统运行性能冲击，这里手动进行了分批次读取，开发者可按需修改该值。
        int batchSize = resultList instanceof Page ? 0 : 1000;
        // NOTE: 这里只是包含了关联数据，聚合计算数据没有包含。
        // 主要原因是，由于聚合字段通常被视为普通字段使用，不会在group by的从句中出现，语义上也不会在此关联。
        this.buildRelationForDataList(resultList, MyRelationParam.normal(), batchSize);
        return resultList;
    }

    private SchClusterNode buildDefaultValue(SchClusterNode schClusterNode) {
        if (schClusterNode.getId() == null) {
            schClusterNode.setId(idGenerator.nextLongId());
        }
        TokenData tokenData = TokenData.takeFromRequest();
        schClusterNode.setCreateUserId(tokenData.getUserId());
        schClusterNode.setUpdateUserId(tokenData.getUserId());
        Date now = new Date();
        schClusterNode.setUpdateTime(now);
        schClusterNode.setCreateTime(now);
        schClusterNode.setIsDelete(GlobalDeletedFlag.NORMAL);
        return schClusterNode;
    }

    /**
     * 集群节点统计。
     *
     * @return 应答结果对象，包含对象详情。
     */
    @Override
    public Map<String,Object> clusterNodeStatistics() {
        Map<String,Object> map = new HashMap<>();
        //主节点统计
        List<SchClusterNode> schClusterNodeList=schClusterNodeMapper.selectList( null);
        if(schClusterNodeList.isEmpty()){
            map.put("masterNode",0);
            map.put("slaveNode",0);
            map.put("ready",0);
            map.put("noReady",0);
            map.put("totalNode",schClusterNodeList.size());
            return map;
        }
        //根据主从节点分类
        Map<String, List<SchClusterNode>> roleMap = schClusterNodeList.stream().collect(Collectors.groupingBy(SchClusterNode::getNodeRoles));
        map.put("master",roleMap.get("master")==null?0:roleMap.get("master").size());
        map.put("slave",roleMap.get("slave")==null?0:roleMap.get("slave").size());
        //根据状态分类
        Map<String, List<SchClusterNode>> statusMap = schClusterNodeList.stream().collect(Collectors.groupingBy(SchClusterNode::getNodeStatus));
        map.put("ready",statusMap.get("ready")==null?0:statusMap.get("ready").size());
        map.put("noReady",statusMap.get("noReady")==null?0:statusMap.get("noReady").size());
        map.put("totalNode",schClusterNodeList.size());
        return map;
    }
}
