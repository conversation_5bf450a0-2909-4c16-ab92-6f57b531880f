package supie.webadmin.app.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import supie.webadmin.app.service.*;
import supie.webadmin.app.dao.*;
import supie.webadmin.app.model.*;
import supie.common.core.base.dao.BaseDaoMapper;
import supie.common.core.constant.GlobalDeletedFlag;
import supie.common.core.object.TokenData;
import supie.common.core.object.MyRelationParam;
import supie.common.core.base.service.BaseService;
import supie.common.core.util.MyModelUtil;
import supie.common.sequence.wrapper.IdGeneratorWrapper;
import com.github.pagehelper.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 集群管理表数据操作服务类。
 *
 * <AUTHOR> -rf .bug
 * @date 2025-05-16
 */
@Slf4j
@Service("schClusterManagerService")
public class SchClusterManagerServiceImpl extends BaseService<SchClusterManager, Long> implements SchClusterManagerService {

    @Autowired
    private IdGeneratorWrapper idGenerator;
    @Autowired
    private SchClusterManagerMapper schClusterManagerMapper;

    /**
     * 返回当前Service的主表Mapper对象。
     *
     * @return 主表Mapper对象。
     */
    @Override
    protected BaseDaoMapper<SchClusterManager> mapper() {
        return schClusterManagerMapper;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public SchClusterManager saveNew(SchClusterManager schClusterManager) {
        schClusterManagerMapper.insert(this.buildDefaultValue(schClusterManager));
        return schClusterManager;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveNewBatch(List<SchClusterManager> schClusterManagerList) {
        if (CollUtil.isNotEmpty(schClusterManagerList)) {
            schClusterManagerList.forEach(this::buildDefaultValue);
            schClusterManagerMapper.insertList(schClusterManagerList);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean update(SchClusterManager schClusterManager, SchClusterManager originalSchClusterManager) {
        schClusterManager.setCreateUserId(originalSchClusterManager.getCreateUserId());
        schClusterManager.setUpdateUserId(TokenData.takeFromRequest().getUserId());
        schClusterManager.setUpdateTime(new Date());
        schClusterManager.setCreateTime(originalSchClusterManager.getCreateTime());
        // 这里重点提示，在执行主表数据更新之前，如果有哪些字段不支持修改操作，请用原有数据对象字段替换当前数据字段。
        UpdateWrapper<SchClusterManager> uw = this.createUpdateQueryForNullValue(schClusterManager, schClusterManager.getId());
        return schClusterManagerMapper.update(schClusterManager, uw) == 1;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean remove(Long id) {
        return schClusterManagerMapper.deleteById(id) == 1;
    }

    @Override
    public List<SchClusterManager> getSchClusterManagerList(SchClusterManager filter, String orderBy) {
        return schClusterManagerMapper.getSchClusterManagerList(filter, orderBy);
    }

    @Override
    public List<SchClusterManager> getSchClusterManagerListWithRelation(SchClusterManager filter, String orderBy) {
        List<SchClusterManager> resultList = schClusterManagerMapper.getSchClusterManagerList(filter, orderBy);
        // 在缺省生成的代码中，如果查询结果resultList不是Page对象，说明没有分页，那么就很可能是数据导出接口调用了当前方法。
        // 为了避免一次性的大量数据关联，规避因此而造成的系统运行性能冲击，这里手动进行了分批次读取，开发者可按需修改该值。
        int batchSize = resultList instanceof Page ? 0 : 1000;
        this.buildRelationForDataList(resultList, MyRelationParam.full(), batchSize);
        return resultList;
    }

    @Override
    public List<SchClusterManager> getGroupedSchClusterManagerListWithRelation(
            SchClusterManager filter, String groupSelect, String groupBy, String orderBy) {
        List<SchClusterManager> resultList =
                schClusterManagerMapper.getGroupedSchClusterManagerList(filter, groupSelect, groupBy, orderBy);
        // 在缺省生成的代码中，如果查询结果resultList不是Page对象，说明没有分页，那么就很可能是数据导出接口调用了当前方法。
        // 为了避免一次性的大量数据关联，规避因此而造成的系统运行性能冲击，这里手动进行了分批次读取，开发者可按需修改该值。
        int batchSize = resultList instanceof Page ? 0 : 1000;
        // NOTE: 这里只是包含了关联数据，聚合计算数据没有包含。
        // 主要原因是，由于聚合字段通常被视为普通字段使用，不会在group by的从句中出现，语义上也不会在此关联。
        this.buildRelationForDataList(resultList, MyRelationParam.normal(), batchSize);
        return resultList;
    }

    private SchClusterManager buildDefaultValue(SchClusterManager schClusterManager) {
        if (schClusterManager.getId() == null) {
            schClusterManager.setId(idGenerator.nextLongId());
        }
        TokenData tokenData = TokenData.takeFromRequest();
        schClusterManager.setCreateUserId(tokenData.getUserId());
        schClusterManager.setUpdateUserId(tokenData.getUserId());
        Date now = new Date();
        schClusterManager.setUpdateTime(now);
        schClusterManager.setCreateTime(now);
        schClusterManager.setIsDelete(GlobalDeletedFlag.NORMAL);
        MyModelUtil.setDefaultValue(schClusterManager, "clusterMark", new Date());
        return schClusterManager;
    }

    /**
     * 集群统计。
     *
     * @return 应答结果对象，包含对象详情。
     */
    @Override
    public Map<String,Object> clusterStatistics() {
        Map<String,Object> map = new HashMap<>();
        //主节点统计
        List<SchClusterManager> schClusterManagerList=schClusterManagerMapper.selectList( null);
        map.put("total",schClusterManagerList.size());
        if(schClusterManagerList.isEmpty()){
            return map;
        }
        //根据状态分类
        Map<String, List<SchClusterManager>> statusMap = schClusterManagerList.stream().collect(Collectors.groupingBy(SchClusterManager::getClusterStatus));
        map.put("Available",statusMap.get("Available")==null?0:statusMap.get("Available").size());
        map.put("Deactivate",statusMap.get("Deactivate")==null?0:statusMap.get("Deactivate").size());
        return map;
    }
}
