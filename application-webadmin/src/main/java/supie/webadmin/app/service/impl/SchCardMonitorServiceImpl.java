package supie.webadmin.app.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import supie.common.core.annotation.MyDataSource;
import supie.webadmin.app.service.*;
import supie.webadmin.app.dao.*;
import supie.webadmin.app.model.*;
import supie.common.core.base.dao.BaseDaoMapper;
import supie.common.core.constant.GlobalDeletedFlag;
import supie.common.core.object.TokenData;
import supie.common.core.object.MyRelationParam;
import supie.common.core.base.service.BaseService;
import supie.common.sequence.wrapper.IdGeneratorWrapper;
import com.github.pagehelper.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import supie.webadmin.config.DataSourceType;

import java.util.*;

/**
 * 服务卡监控(NPU,GPU监控)数据操作服务类。
 *
 * <AUTHOR> -rf .bug
 * @date 2025-05-16
 */
@Slf4j
@Service("schCardMonitorService")
@MyDataSource(DataSourceType.CLICKHOME)
public class SchCardMonitorServiceImpl extends BaseService<SchCardMonitor, Long> implements SchCardMonitorService {

    @Autowired
    private IdGeneratorWrapper idGenerator;
    @Autowired
    private SchCardMonitorMapper schCardMonitorMapper;
    @Autowired
    private SchComputeDeviceService schComputeDeviceService;

    /**
     * 返回当前Service的主表Mapper对象。
     *
     * @return 主表Mapper对象。
     */
    @Override
    protected BaseDaoMapper<SchCardMonitor> mapper() {
        return schCardMonitorMapper;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public SchCardMonitor saveNew(SchCardMonitor schCardMonitor) {
        schCardMonitorMapper.insert(this.buildDefaultValue(schCardMonitor));
        return schCardMonitor;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveNewBatch(List<SchCardMonitor> schCardMonitorList) {
        if (CollUtil.isNotEmpty(schCardMonitorList)) {
            schCardMonitorList.forEach(this::buildDefaultValue);
            schCardMonitorMapper.insertList(schCardMonitorList);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean update(SchCardMonitor schCardMonitor, SchCardMonitor originalSchCardMonitor) {
        schCardMonitor.setCreateUserId(originalSchCardMonitor.getCreateUserId());
        schCardMonitor.setUpdateUserId(TokenData.takeFromRequest().getUserId());
        schCardMonitor.setUpdateTime(new Date());
        schCardMonitor.setCreateTime(originalSchCardMonitor.getCreateTime());
        // 这里重点提示，在执行主表数据更新之前，如果有哪些字段不支持修改操作，请用原有数据对象字段替换当前数据字段。
        UpdateWrapper<SchCardMonitor> uw = this.createUpdateQueryForNullValue(schCardMonitor, schCardMonitor.getId());
        return schCardMonitorMapper.update(schCardMonitor, uw) == 1;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean remove(Long id) {
        return schCardMonitorMapper.deleteById(id) == 1;
    }

    @Override
    public List<SchCardMonitor> getSchCardMonitorList(SchCardMonitor filter, String orderBy) {
        return schCardMonitorMapper.getSchCardMonitorList(filter, orderBy);
    }

    @Override
    public List<SchCardMonitor> getSchCardMonitorListWithRelation(SchCardMonitor filter, String orderBy) {
        List<SchCardMonitor> resultList = schCardMonitorMapper.getSchCardMonitorList(filter, orderBy);
        // 在缺省生成的代码中，如果查询结果resultList不是Page对象，说明没有分页，那么就很可能是数据导出接口调用了当前方法。
        // 为了避免一次性的大量数据关联，规避因此而造成的系统运行性能冲击，这里手动进行了分批次读取，开发者可按需修改该值。
        int batchSize = resultList instanceof Page ? 0 : 1000;
        this.buildRelationForDataList(resultList, MyRelationParam.normal(), batchSize);
        return resultList;
    }

    @Override
    public List<SchCardMonitor> getGroupedSchCardMonitorListWithRelation(
            SchCardMonitor filter, String groupSelect, String groupBy, String orderBy) {
        List<SchCardMonitor> resultList =
                schCardMonitorMapper.getGroupedSchCardMonitorList(filter, groupSelect, groupBy, orderBy);
        // 在缺省生成的代码中，如果查询结果resultList不是Page对象，说明没有分页，那么就很可能是数据导出接口调用了当前方法。
        // 为了避免一次性的大量数据关联，规避因此而造成的系统运行性能冲击，这里手动进行了分批次读取，开发者可按需修改该值。
        int batchSize = resultList instanceof Page ? 0 : 1000;
        // NOTE: 这里只是包含了关联数据，聚合计算数据没有包含。
        // 主要原因是，由于聚合字段通常被视为普通字段使用，不会在group by的从句中出现，语义上也不会在此关联。
        this.buildRelationForDataList(resultList, MyRelationParam.normal(), batchSize);
        return resultList;
    }

    private SchCardMonitor buildDefaultValue(SchCardMonitor schCardMonitor) {
        if (schCardMonitor.getId() == null) {
            schCardMonitor.setId(idGenerator.nextLongId());
        }
        //TokenData tokenData = TokenData.takeFromRequest();
        schCardMonitor.setCreateUserId(1923195822141345792l);
        schCardMonitor.setUpdateUserId(1923195822141345792l);
        Date now = new Date();
        schCardMonitor.setUpdateTime(now);
        schCardMonitor.setCreateTime(now);
        schCardMonitor.setIsDelete(GlobalDeletedFlag.NORMAL);
        return schCardMonitor;
    }

    /**
     * 根据显卡id查询监控。
     *
     * @return 主表Mapper对象。
     */
    @Override
    public SchCardMonitor getNpuMonitoringByComputeDeviceId(Long computeDeviceId, Date ts) {
        return schCardMonitorMapper.getNpuMonitoringByComputeDeviceId(computeDeviceId,ts);
    }

    /**
     * 根据主机id查询监控。
     *
     * @return 主表Mapper对象。
     */
    @Override
    public List<SchCardMonitor> getNpuMonitoringByResourceInfoId(Long resourceInfoId, Date ts) {
        return schCardMonitorMapper.getNpuMonitoringByResourceInfoId(resourceInfoId,ts);
    }

    /**
     * 获取指定数据
     * @return
     */
    @Override
    public List<SchCardMonitor> getSchCardMonitorListByFilter(SchCardMonitor schCardMonitor,List<Long> resourceIdList) {
        return schCardMonitorMapper.statisticalIndicators(schCardMonitor,resourceIdList);
    }

    @Override
    public void saveNewBatchs(List<SchCardMonitor> schCardMonitorList) {
        if (CollUtil.isNotEmpty(schCardMonitorList)) {
            //schCardMonitorList.forEach(this::buildDefaultValue);
            schCardMonitorMapper.insertList(schCardMonitorList);
        }
    }

    /**
     * 查询出最近24小时内数据
     * @param schResourceInfoIdList 服务器id列表
     * @return 返回对应的集合
     */
    @Override
    public List<SchCardMonitor> getSchCardMonitorListByidList(List<Long> schResourceInfoIdList) {
        return schCardMonitorMapper.getSchCardMonitorListByIdList(schResourceInfoIdList);
    }

    @Override
    public List<SchCardMonitor> getSchCardMonitorListByids(List<Long> schComputeDeviceIdList) {
        return schCardMonitorMapper.getSchCardMonitorListByids(schComputeDeviceIdList);
    }

    @Override
    public List<SchCardMonitor> getSchCardMonitorListByResourceId(Long resourceId) {
        return schCardMonitorMapper.getSchCardMonitorListByResourceId(resourceId);
    }
}
