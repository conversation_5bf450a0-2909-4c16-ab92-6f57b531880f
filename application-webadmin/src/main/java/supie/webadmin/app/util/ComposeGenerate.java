package supie.webadmin.app.util;

import cn.hutool.core.util.ObjUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;

import com.jcraft.jsch.*;

import freemarker.template.Configuration;
import freemarker.template.Template;
import freemarker.template.TemplateException;
import jakarta.annotation.Resource;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import supie.common.core.constant.GlobalDeletedFlag;
import supie.common.core.exception.MyRuntimeException;

import supie.common.sequence.wrapper.IdGeneratorWrapper;
import supie.webadmin.app.dao.*;
import supie.webadmin.app.dto.RemoteTask;
import supie.webadmin.app.model.*;
import supie.webadmin.app.service.SchTaskInfoService;
import supie.webadmin.app.service.SchTaskMonitoringService;
import supie.webadmin.app.util.docker.ContainerConfig;

import java.io.*;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.sql.Timestamp;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static cn.hutool.log.StaticLog.info;

@Component
@Slf4j
@Setter
@Getter
public class ComposeGenerate {

   // @Value("${fileLocationManagement.jarPosition}")
    private String serverLocation;
    
   // @Value("${publicAndPrivateKey.RemoteHost_PRIVATE_KEY}")
    private String privateKey;

    @Resource
    private SchResourceInfoMapper schResourceInfoMapper;


    /**
     * 服务器compose 文件存储根路径
     */
    @Value("${application.composeBasePath}")
    private String remotePath;

    @Resource
    private SchTaskImageMapper schTaskImageMapper;

    @Resource
    private SchTaskInfoMapper schTaskInfoMapper;

    @Resource
    private SshConnectionUtil sshConnectionUtil;

    @Resource
    private SchTaskAllocationMapper schTaskAllocationMapper;

    @Resource
    private SchTaskMonitoringMapper  schTaskMonitoringMapper;
    @Resource
    private IdGeneratorWrapper idGeneratorWrapper;

    @Resource
    private  SchClusterNodeMapper schClusterNodeMapper;

    @Resource
    private SchTaskInfoService schTaskInfoService;








    /**
     * 容器NPU路径
     */
    private static final String CONTAINER_NPU_PATH = "/dev/vdavinci";


    /**
     *  服务VNPU路径
     */
    private static final String SERVER_NPU_PATH = "/dev/davinci";


    /**
     * 容器物理卡
     */
    private static final String PHYSICS_CONTAINER_NPU_PATH = "/dev/vdavinci";


    /**
     * 服务器物理卡
     */
    private static final String PHYSICS_SERVER_NPU_PATH = "/dev/davinci";


    /**
     * compose 文件存储基路径
     */
    private static final String COMPOSE_STORE_PATH="/data/applications/lmd-formal/backend/compose";


    /**
     * docker 状态信息
     */
    Pattern DOCKER_STATS = Pattern.compile(
            "^(\\S+)\\s+(\\S+.*?\\S)\\s+(\\S+%)\\s+(\\S+)\\s+/\\s+(\\S+)\\s+(\\S+%)\\s+(\\S+)\\s+/\\s+(\\S+)\\s+(\\S+)\\s+/\\s+(\\S+)\\s+(\\d+)$"
    );


    /**
     * docker 使用率 正则解析
     */
    private static final Pattern DIGIT_PATTERN = Pattern.compile("(\\d+(\\.\\d+)?%?)");
    @Autowired
    private SchTaskMonitoringService schTaskMonitoringService;

    @Resource
    private  SchContainerManagerMapper schContainerManagerMapper;

    /**
     * nginx 默认网络名称
     */
    @Value("${nginx.network-name}")
    private String networkName;



    /**
     * 生成Docker Compose文件并启动容器
     *
     * @param code 资源信息对象
     * @param vnpu VNPU设备ID列表
     * @param imageId 镜像ID
     * @param taskId 任务ID
     * @return 更新后的任务信息
     * @throws IOException 如果文件操作失败
     */
    public SchTaskInfo generateCompose(SchResourceInfo code, List<Integer> vnpu, Long imageId, Long taskId) throws IOException {
        log.info("ComposeGenerate  generateCompose  method  start");
        //this.taskId = taskId;
        SchTaskInfo st = schTaskInfoMapper.selectById(
                Objects.requireNonNull(taskId, "任务ID为空")
        );
        //this.computeDeviceId=st.getComputeDeviceId();
       // this.resourceId=st.getResourceId();
       // this.imageId=imageId;
        SchResourceInfo info = schResourceInfoMapper.selectById(code.getId());
        Session session = null;
        SshConnection connection = null;
        Map<String, Long> params = Map.of(
                "taskId", taskId,
                "imageId", st.getTaskImageId(),
                "computeDeviceId", st.getComputeDeviceId(),
                "resourceId", st.getResourceId()
        );
        try {
            log.info("ComposeGenerate  generateCompose  method  start  params {}",params.get("taskId"));
           connection = sshConnectionUtil.createConnection(info.getHostIp(), info.getPort(), info.getLoginName(), info.getPassword(), 30000);
            session = connection.getSession();
            String fullPath = COMPOSE_STORE_PATH + "/" + taskId;
            // 启动compose 任务状态（（枚举：pending待审批、approval审核通过、rejected拒绝、queued排队中、starting启动中、running运行中、finished完成、failed失败、cancelled取消、rejected已拒绝）
            byte[] composeBytes = buildStartTemplate(vnpu,  imageId, code.getHostIp(),params);
            transformServer(composeBytes,  session, fullPath + "/compose.yml",params);
            composeExec( session, "cd " + fullPath + " && docker-compose up -d",params);
            return schTaskInfoMapper.selectById(taskId);
        } catch (JSchException e) {
            log.error("ComposeGenerate  generateCompose  method  execute fail {}  ",e.getMessage());
            schTaskInfoMapper.update(
                    new LambdaUpdateWrapper<SchTaskInfo>()
                            .eq(SchTaskInfo::getId, taskId)
                            .set(SchTaskInfo::getFailReason, e.getMessage())
                            .set(SchTaskInfo::getUpdateTime, new Date())
                            .set(SchTaskInfo::getStatus, "failed")
            );
            throw new RuntimeException(e);
        } finally {
            if (session != null && session.isConnected()) {
                connection.close();
            }
        }
    }




    /**
     * 远程任务启动，使用提供的模板启动容器
     *
     * @param code 资源信息对象
     * @param vnpu VNPU设备ID列表
     * @param taskId 任务ID
     * @param imageId 镜像ID
     * @throws IOException 如果文件操作失败
     */

    public  SchTaskInfo ordinaryStart(SchResourceInfo code, List<Integer> vnpu, Long imageId, Long taskId) throws IOException {
        log.info("ComposeGenerate  ordinaryStart  method  start");
        SchResourceInfo info = schResourceInfoMapper.selectById(code.getId());
        String fullPath = COMPOSE_STORE_PATH + "/" + taskId;
        SshConnection connection = null;
        //this.taskId = taskId;
       // this.imageId=imageId;
        SchTaskInfo st = schTaskInfoMapper.selectById(
                Objects.requireNonNull(taskId, "任务ID为空")
        );
        //this.computeDeviceId=st.getComputeDeviceId();
       // this.resourceId=st.getResourceId();
        Map<String, Long> params = Map.of(
                "taskId", taskId,
                "imageId", st.getTaskImageId(),
                "computeDeviceId", st.getComputeDeviceId(),
                "resourceId", st.getResourceId()
        );
        try {
            log.info("ComposeGenerate  ordinaryStart  method  start 当前任务ID:{}",taskId);
            connection = sshConnectionUtil.createConnection(info.getHostIp(), info.getPort(), info.getLoginName(), info.getPassword(), 30000);
            byte[] composeBytes = buildStartTemplate(vnpu, imageId, code.getHostIp(),params);
            transformServer(composeBytes, connection.getSession(), fullPath + "/compose.yml",params);
            composeExec(connection.getSession(), "cd " + fullPath + " && docker-compose up -d",params);
        } catch (JSchException e) {
            log.error("ComposeGenerate  ordinaryStart  method  execute fail {}  ",e.getMessage());
            schTaskInfoMapper.update(
                    new LambdaUpdateWrapper<SchTaskInfo>()
                            .eq(SchTaskInfo::getId, taskId)
                            .set(SchTaskInfo::getFailReason, e.getMessage())
                            .set(SchTaskInfo::getUpdateTime, new Date())
                            .set(SchTaskInfo::getStatus, "failed")
            );
            throw new RuntimeException(e);
        } finally {
            assert connection != null;
            if (connection.getSession() != null){
                connection.close();
            }
        }
        return schTaskInfoMapper.selectById(taskId);
    }


    /**
     *  用于启动远程任务 compose 文件模板
     * @param code 资源信息对象
     * @param taskId 任务ID
     * @param templateBytes 模板文件字节数组
     * @throws IOException 传输异常
     */
    public  void remoteTask(SchResourceInfo code,  Long taskId,byte[] templateBytes,RemoteTask remoteTask) throws IOException {
        log.info("ComposeGenerate  remoteTask  method  execute start ");
        SchResourceInfo info = schResourceInfoMapper.selectById(code.getId());
        String fullPath = COMPOSE_STORE_PATH + "/" + taskId;
        SshConnection connection = null;
        //this.taskId = taskId;
        SchTaskInfo st = schTaskInfoMapper.selectById(
                Objects.requireNonNull(taskId, "任务ID为空")
        );
       // this.computeDeviceId=st.getComputeDeviceId();
       // this.resourceId=st.getResourceId();
        Map<String, Long> params = Map.of(
                "taskId", taskId,
                "imageId", st.getTaskImageId(),
                "computeDeviceId", Objects.isNull(st.getComputeDeviceId()) ?0L:st.getComputeDeviceId(),
                "resourceId", st.getResourceId()
        );
        try {
            log.info("ComposeGenerate  remoteTask  method  execute start 当前任务ID:{}",taskId);
            connection = sshConnectionUtil.createConnection(info.getHostIp(), info.getPort(), info.getLoginName(), info.getPassword(), 300000);
            this.transformServer(templateBytes, connection.getSession(), fullPath + "/compose.yml",params);
            this.composeExec(connection.getSession(), "cd " + fullPath + " && docker-compose up -d",params);
            if(
                    ( ObjUtil.isNotEmpty(remoteTask.getCodeMark()) && remoteTask.getCodeMark().equals(1) )
                            ||
                            (ObjUtil.isNotEmpty(remoteTask.getCodeMark()) && remoteTask.getCodeMark().equals(2))
            ){
                log.info("ComposeGenerate  remoteTask 构建nginx 配置   当前任务ID:{}",taskId);
                schTaskInfoService.nginxProxy(code.getId(),taskId,remoteTask.getContainerPort());
                schContainerManagerMapper.update(
                        new LambdaUpdateWrapper<SchContainerManager>()
                                .eq(SchContainerManager::getTaskInfoId, taskId)
                                .set(SchContainerManager::getExportPort, JSON.toJSONString(remoteTask.getExport()))
                );
            }
        } catch (Exception e) {
            log.error("ComposeGenerate  remoteTask  method  execute fail {}  taskId:{} ",e.getMessage(),params.get("taskId"));
            schTaskInfoMapper.update(
                    new LambdaUpdateWrapper<SchTaskInfo>()
                            .eq(SchTaskInfo::getId, taskId)
                            .set(SchTaskInfo::getFailReason, e.getMessage())
                            .set(SchTaskInfo::getUpdateTime, new Date())
                            .set(SchTaskInfo::getStatus, "failed")
            );
            throw new MyRuntimeException(e);
        } finally {
            assert connection != null;
            if (connection.getSession() != null){
                connection.close();
            }
        }
    }






    /**
     * 远程任务启动，使用提供的模板启动容器
     *
     * @param imageId  镜像信息id
     * @param ip VNPU设备ID列表
     * @param npuId 任务ID
     * @throws IOException 如果文件操作失败
     */
    private  byte[] buildStartTemplate(List<Integer> npuId,Long imageId,String ip,Map<String,Long> params){
        SchTaskImage schTaskImage = schTaskImageMapper.selectById(imageId);
       // this.imageId=imageId;
        try {
            Configuration cfg = new Configuration(Configuration.VERSION_2_3_31);

            cfg.setClassForTemplateLoading(ComposeGenerate.class, "/templates/code");
            Template template = cfg.getTemplate("base.ftl");
            List<String> devices = new ArrayList<>();
            for (Integer item : npuId) {
                String device = CONTAINER_NPU_PATH + item + ":" + SERVER_NPU_PATH + item;
                devices.add(device);
            }
            Map<String, Object> dataModel = new HashMap<>();
            dataModel.put("imageName", schTaskImage.getImageName());
            dataModel.put("outPort",String.valueOf(sshConnectionUtil.getFreePort(ip)));
            dataModel.put("devices", devices);
            dataModel.put("serverStorePath",String.valueOf(params.get("taskId")));
            StringWriter writer = new StringWriter();
            template.process(dataModel, writer);
            log.info("Successfully built start template: {}", writer);
            return writer.toString().getBytes(StandardCharsets.UTF_8);
        } catch (IOException | TemplateException e) {
            log.error("ComposeGenerate  buildStartTemplate  method  execute fail {}  taskId:{} ",e.getMessage(),params.get("taskId"));
            schTaskInfoMapper.update(
                    new LambdaUpdateWrapper<SchTaskInfo>()
                            .eq(SchTaskInfo::getId, params.get("taskId"))
                            .set(SchTaskInfo::getFailReason, "compose 模板构建失败")
                            .set(SchTaskInfo::getUpdateTime, new Date())
                            .set(SchTaskInfo::getStatus, "failed")
            );
            throw new MyRuntimeException(e);
        }
    }


    /**
     *  构建compose 文件木棒  远程任务启动模板构建
     * @param npuId vnpuid 列表
     * @param remoteTask 远程信息参数
     * @param hostPort 宿主机端口列表
     * @param params 参数 次方法只用到 taskId
     * @return  compose 文件字节数组
     */
    public byte[] buildRemoteTaskTemplate(List<Integer> npuId, RemoteTask remoteTask, List<String> hostPort,Map<String,Long> params) {
        try {
            Configuration cfg = new Configuration(Configuration.VERSION_2_3_31);
            Template template;
            Map<String, Object> dataModel = new HashMap<>();
            List<String> devices = new ArrayList<>();


            if(
                    ( ObjUtil.isNotEmpty(remoteTask.getCodeMark()) && remoteTask.getCodeMark().equals(1) )
                    ||
                            (ObjUtil.isNotEmpty(remoteTask.getCodeMark()) && remoteTask.getCodeMark().equals(2))
            ){
                cfg.setClassForTemplateLoading(ComposeGenerate.class, "/templates/code");
                template=cfg.getTemplate("code.ftl");
                // 当前容器网路别名
                dataModel.put("netWorkName","code-"+params.get("taskId"));
                dataModel.put("network",networkName);
            }else{
                cfg.setClassForTemplateLoading(ComposeGenerate.class, "/templates/remotetask");
                template=cfg.getTemplate("train.ftl");
            }

            if ("physical".equals(remoteTask.getNeedResource())) {
                for (Integer item : npuId) {
                    String device = PHYSICS_CONTAINER_NPU_PATH + item + ":"+ PHYSICS_SERVER_NPU_PATH + item;
                    devices.add(device);
                }
            } else if ("vnpu".equals(remoteTask.getNeedResource())) {
                for (Integer item : npuId) {
                    String device = CONTAINER_NPU_PATH + item + ":" + SERVER_NPU_PATH + item;
                    devices.add(device);
                }
            }
            dataModel.put("imageName", remoteTask.getImageName());
            dataModel.put("resourceType",remoteTask.getNeedResource());
            if ("physical".equals(remoteTask.getNeedResource())) {
                dataModel.put("devices", devices);
            }
            if("vnpu".equals(remoteTask.getNeedResource())){
                dataModel.put("vnpuDevices", devices);
            }
            dataModel.put("containerPath",remoteTask.getContainerPath());
            dataModel.put("serverMountPath",remoteTask.getContainerMapPath());
            dataModel.put("hostPort",hostPort );
            dataModel.put("containerPort", remoteTask.getContainerPort());
            dataModel.put("startCommand", remoteTask.getCommand());
            dataModel.put("envVars", remoteTask.getEnvironment());
            dataModel.put("device",devices);
            StringWriter writer = new StringWriter();
            template.process(dataModel, writer);
            log.info("Successfully built remote task template");
            log.info("{}",writer);
            return writer.toString().getBytes(StandardCharsets.UTF_8);
        } catch (IOException | TemplateException e) {
            log.error("ComposeGenerate  buildRemoteTaskTemplate  method  execute fail {}   taskId:{} ",e.getMessage(),params.get("taskId"));
            schTaskInfoMapper.update(
                    new LambdaUpdateWrapper<SchTaskInfo>()
                            .eq(SchTaskInfo::getId, params.get("taskId"))
                            .set(SchTaskInfo::getFailReason, "compose 模板构建失败"+e.getMessage())
                            .set(SchTaskInfo::getUpdateTime, new Date())
                            .set(SchTaskInfo::getStatus, "failed")
            );

            throw new MyRuntimeException(e);
        }
    }


    /**
     *  SSH 连接初始化
     * @param resourceInfoId  服务器信息表主键
     * @param params 参数 这里只用到 taskId
     * @return SHH 会话对象
     */
    public Session initConnect(Long resourceInfoId,Map<String,Long> params) {
        SchResourceInfo schResourceInfo = schResourceInfoMapper.selectById(resourceInfoId);
        try {
            JSch jsch = new JSch();
            //String decryptedPassword = RsaUtil.decrypt(schResourceInfo.getPassword(), privateKey);
            Session session = jsch.getSession(schResourceInfo.getLoginName(), schResourceInfo.getHostIp(), schResourceInfo.getPort());
            session.setPassword(schResourceInfo.getPassword());
            session.setConfig("StrictHostKeyChecking", "no");
            session.connect();
            log.info("Successfully connected to remote host: {}",schResourceInfo.getHostIp());
            return session;
        } catch (Exception e) {
            log.error("Failed to connect to remote host: {}", e.getMessage());
            schTaskInfoMapper.update(
                    new LambdaUpdateWrapper<SchTaskInfo>()
                            .eq(SchTaskInfo::getId,params.get("taskId"))
                            .set(SchTaskInfo::getFailReason, e.getMessage())
                            .set(SchTaskInfo::getUpdateTime, new Date())
                            .set(SchTaskInfo::getStatus, "failed")
            );

            throw new MyRuntimeException("服务器连接失败: " + e.getMessage());
        }
    }


    /**
     *  将compose 文件传输到服务器下
     * @param dataByte 构建好的compose 文件字节数组
     * @param session SSH 会话对象
     * @param path compose 存储路径
     * @param params 本方法里面只用到 taskId
     */
    private void transformServer(byte[] dataByte, Session session, String path,Map<String,Long> params ) {
        ChannelSftp sftpChannel = null;
        try {
            sftpChannel = (ChannelSftp) session.openChannel("sftp");
            sftpChannel.connect();
            String parentPath = path.substring(0, path.lastIndexOf('/'));
            circleMakeDirs(parentPath, sftpChannel);
            sftpChannel.put(new ByteArrayInputStream(dataByte), path, ChannelSftp.OVERWRITE);
            log.info("Successfully transferred file to path: {}  taskId: {} ",path,params.get("taskId"));
        } catch (JSchException | SftpException e) {
            log.error("ComposeGenerate  transformServer  method  execute fail {}  当前taskId: {}  ",e.getMessage(),params.get("taskId"));
            schTaskInfoMapper.update(
                    new LambdaUpdateWrapper<SchTaskInfo>()
                            .eq(SchTaskInfo::getId, params.get("taskId"))
                            .set(SchTaskInfo::getFailReason, e.getMessage())
                            .set(SchTaskInfo::getUpdateTime, new Date())
                            .set(SchTaskInfo::getStatus, "failed")
            );
            throw new MyRuntimeException("文件传输失败: " + e.getMessage());
        } finally {
            if (sftpChannel != null && sftpChannel.isConnected()) {
                sftpChannel.disconnect();
            }
        }
    }


    /**
     * 用于执行Docker compose up -d 启动文件
     * @param session SSH 连接会话
     * @param command compose 启动命令
     * @param parm 用于传递参数 resourceId  or  taskId or computeDeviceID  or imageId(此处只接收tasKId)
     */
     private void composeExec(Session session, String command,Map<String,Long> parm ) {
        log.info("ComposeGenerate  composeExec method start : current taskId {}",parm.get("taskId"));
        SshConnection sshConnection = new SshConnection();
        sshConnection.setSession(session);
        try {
            sshConnectionUtil.executeCommandCompose(sshConnection, command,parm.get("taskId"));
            String path = command.substring(command.indexOf("cd ") + 3, command.indexOf(" &&"));
            this.verifyContainerStarted(session, path,parm);
        } catch (JSchException e) {
            log.error("ComposeGenerate  composeExec  method  execute fail {}  当前taskId {} ",e.getMessage(),parm.get("taskId"));
            log.error("Failed to execute command: {} - Error: {}", command, e.getMessage());
            schTaskInfoMapper.update(
                    new LambdaUpdateWrapper<SchTaskInfo>()
                            .eq(SchTaskInfo::getId, parm.get("taskId"))
                            .set(SchTaskInfo::getFailReason, e.getMessage())
                            .set(SchTaskInfo::getUpdateTime, new Date())
                            .set(SchTaskInfo::getStatus, "failed")
            );

            throw new MyRuntimeException("命令执行失败: " + e.getMessage());
        } catch (IOException e) {
            log.error("ComposeGenerate  composeExec  method  execute fail {}  当前taskID  {} ",e.getMessage(),parm.get("taskId"));
            schTaskInfoMapper.update(
                    new LambdaUpdateWrapper<SchTaskInfo>()
                            .eq(SchTaskInfo::getId, parm.get("taskId"))
                            .set(SchTaskInfo::getFailReason, e.getMessage())
                            .set(SchTaskInfo::getUpdateTime, new Date())
                            .set(SchTaskInfo::getStatus, "failed")
            );

            throw new RuntimeException(e);
        }
    }


    /**
     * 验证容器是否启动成功
     * @param session SSH 连接会话
     * @param composePath compose 文件路径
     * @param params taskId  resourceId  or  taskId or computeDeviceID  or imageId(此处只接收tasKId)
     */
    private void verifyContainerStarted(Session session, String composePath,Map<String,Long> params) {
        log.info("verifyContainerStarted 开始检测docker 容器状态..takId {}",params.get("taskId"));
        SshConnection sshConnection=null;
        try {
            // 使用 docker-compose ps -a 命令，显示所有容器，包括已停止的
            String command = "cd " + composePath + " && docker-compose ps -a";
             sshConnection = new SshConnection();
            sshConnection.setSession(session);
            // 执行docker ps -a 筛选指令
            String resultPs = sshConnectionUtil.executeCommand(sshConnection, command);
            // 提取容器名称
            List<String> containerNames = this.extractContainerNames(resultPs);
            if (containerNames.isEmpty()) {
                log.error("No containers found after docker-compose up at path: {}", composePath);
                String logsCommand = "cd " + composePath + " && docker-compose logs";
                String logsResult = sshConnectionUtil.executeCommand(sshConnection, logsCommand).trim();
                log.error(" docker run fail  Docker-compose logs: {}  当前任务taskId--{}", logsResult, params.get("taskId"));
                schTaskInfoMapper.update(new LambdaUpdateWrapper<SchTaskInfo>().
                        eq(SchTaskInfo::getId,params.get("taskId"))
                        .set(SchTaskInfo::getUpdateTime, new Date())
                        .set(SchTaskInfo::getStatus,"failed")
                );
                throw new MyRuntimeException("容器启动失败，未找到容器");
            } else {
                String containerName=containerNames.get(0);
                log.info("Containers found: containerName: {}", containerName);
                    // 检测容器运行状态指令 running paused restarting exited created dead removing exited(0) exited(1)
                    String statusCommand = "docker inspect --format='{{.State.Status}}' " + containerName;
                    // 容器运行状态
                    String containerStatus= sshConnectionUtil.executeCommand(sshConnection, statusCommand).trim();
                    log.info("ContainerName: {} ContainerStatus: {}  taskId: {}", containerName, containerStatus,params.get("taskId"));
                    // 赋值记录容器状态
                    String  status= sshConnectionUtil.executeCommand(sshConnection, statusCommand).trim();
                    // 如果状态是exited，获取退出码以区分正常退出和异常退出
                    log.info("container run status is {}  当前任务TaskID: {} ", containerStatus, params.get("taskId"));
                    if ("exited".equals( containerStatus)) {
                        log.info("开始检测容器 退出码.......");
                        // 容器退出码状态 0 完成 1-125 失败 126 容器配置错误 127 命令未找到 128-255
                        String exitCodeCommand = "docker inspect --format='{{.State.ExitCode}}' " + containerName;
                        String code = sshConnectionUtil.executeCommand(sshConnection, exitCodeCommand).trim();
                        log.info("ContainerName: {} ContainerExitCode: {}  taskId: {}", containerName, code,params.get("taskId"));
                        int exitCode = Integer.parseInt(code);
                        // 更新状态信息，添加退出码
                        if (exitCode == 0) {
                            containerStatus = "exited(0)"; // 正常退出
                            log.info("Container {} exited normally with exit code 0 当前任务ID  {}", containerName,params.get("taskId"));
                            schTaskInfoMapper.update(
                                    new LambdaUpdateWrapper<SchTaskInfo>()
                                            .eq(SchTaskInfo::getId,params.get("taskId"))
                                            .set(SchTaskInfo::getContainerName, containerName)
                                            .set(SchTaskInfo::getStatus, "finished")
                                            .set(SchTaskInfo::getContainerStatus,  status)
                                            .set(SchTaskInfo::getExitCode, containerStatus)
                                            .set(SchTaskInfo::getUpdateTime, new Date())
                            );
                        } else {
                            log.info("Container {} exited abnormally with exit code {} 当前任务id  {}", containerName, exitCode,params.get("taskId"));
                            containerStatus = "exited(" + exitCode + ")"; // 非正常退出
                            schTaskInfoMapper.update(
                                    new LambdaUpdateWrapper<SchTaskInfo>()
                                            .eq(SchTaskInfo::getId, params.get("taskId"))
                                            .set(SchTaskInfo::getStatus, "failed")
                                            .set(SchTaskInfo::getContainerName, containerName)
                                            .set(SchTaskInfo::getExitCode, containerStatus)
                                            .set(SchTaskInfo::getContainerStatus, status)
                                            .set(SchTaskInfo::getUpdateTime, new Date())
                                            .set(SchTaskInfo::getUpdateTime, new Date()));

                        }
                    }else {
                        // compose 短任务执行时候 程序较快 导致立马查询时候 任务态与容器态与 docker-compose ps -a 不符合结果 采取定时更新+
                        log.info("设置任务运行中...当前容器状态..{}...当前任务taskId  {}",status,params.get("taskId"));
                        String taskStats = "";
                        if("running".equals(status) ||
                                "created".equals(status) ||
                                "restarting".equals(status)){
                            taskStats = "running";
                        }
                        if("paused".equals(status)){
                            taskStats = "stop";
                        }
                        if("dead".equals(status)){
                            taskStats = "failed";
                        }
                        schTaskInfoMapper.update(new LambdaUpdateWrapper<SchTaskInfo>().
                                eq(SchTaskInfo::getId, params.get("taskId"))
                                .set(SchTaskInfo::getStatus, taskStats)
                                .set(SchTaskInfo::getUpdateTime, new Date())
                                .set(SchTaskInfo::getContainerName, containerName)
                                .set(SchTaskInfo::getContainerStatus, status)
                        )
                        ;

                    }
                String containerId = this.getContainerId(session, containerNames.get(0),params);
                log.info("查询集群节点信息，resourceId: {}  当前任务id  {}", params.get("resourceId"),params.get("taskId"));
                SchClusterNode schClusterNode = schClusterNodeMapper.selectOne(new LambdaQueryWrapper<SchClusterNode>()
                        .eq(SchClusterNode::getResourceInfoId, params.get("resourceId")));
                if (schClusterNode == null) {
                    log.warn("未找到resourceId为{}的集群节点信息，将使用默认节点ID", params.get("resourceId"));
                }
                schContainerManagerMapper.insert(
                        new SchContainerManager()
                                .setId(idGeneratorWrapper.nextLongId())
                                .setContainerId(containerId.trim())
                                .setTaskInfoId(params.get("taskId"))
                                .setTaskImageId(params.get("imageId"))
                                .setClusterNodeId(schClusterNode == null ? 1934508590266060800L : schClusterNode.getId()) //TODO 开发临时使用
                                .setIsDelete(GlobalDeletedFlag.NORMAL)
                                .setContainerName(containerName)
                                .setContainerStatus(status)
                                .setContainerCreateTime(new Date())
                                .setContainerOperation("start")
                                .setContainerOperationTime(new Date())
                                .setCreateTime(new Date())
                                .setUpdateTime(new Date())
                );
            }
        } catch (Exception e) {
            // TODO 失败更新任务
            log.error("ComposeGenerate  verifyContainerStarted  method  execute fail {}  当前taskId {}  ",e.getMessage(),params.get("taskId"));
            schTaskInfoMapper.update(new LambdaUpdateWrapper<SchTaskInfo>().
                    eq(SchTaskInfo::getId,params.get("taskId"))
                            .set(SchTaskInfo::getFailReason, e.getMessage())
                    .set(SchTaskInfo::getStatus, "failed")
                    .set(SchTaskInfo::getUpdateTime, new Date())
            );
        } finally {
            if (sshConnection!=null) {
                sshConnection.close();
            }
        }
    }

    /**
     * 根据容器状态确定任务状态 exited(1)
     * @param containerStatus 容器状态字符串，可能包含多个状态，用逗号分隔
     * @return 任务状态
     */
    private String determineTaskStatus(String containerStatus) {
        if (containerStatus.contains("running")) {
            return "running";
        } else if (containerStatus.contains("exited(0)") && !containerStatus.matches(".*exited\\((?!0)\\d+\\).*")) {
            // 只有正常退出的情况
            return "finished";
        } else if (!containerStatus.contains("exited(0)") && containerStatus.matches(".*exited\\(\\d+\\).*")) {
            // 只有异常退出的情况
            return "failed";
        } else if (containerStatus.contains("exited(0)") && containerStatus.matches(".*exited\\((?!0)\\d+\\).*")) {
            // 混合有正常退出和异常退出的情况
            return "partial_failure";
        } else {
            // 其他状态（如created, restarting等）
            return "pending";
        }
    }

    /**
     *  构建compose 启动模板根据指定参数
     * @param containerConfigs compose 配置参数
     * @param params 参数 此处只有 taskId
     * @return 字节数组
     */
    public byte[] generateComposeConfig(List<ContainerConfig> containerConfigs,Map<String,Long> params) {
        try {
            Configuration cfg = new Configuration(Configuration.VERSION_2_3_31);
            cfg.setClassForTemplateLoading(DockerComposeGenerator.class, "/templates/remotetask");
            Template template = cfg.getTemplate("common.ftl");

            Map<String, Object> dataModel = new HashMap<>();
            dataModel.put("containers", containerConfigs);
            StringWriter writer = new StringWriter();
            template.process(dataModel, writer);
            log.info("Successfully generated Docker Compose configuration  {}",writer.toString());
            return writer.toString().getBytes(StandardCharsets.UTF_8);
        } catch (IOException | TemplateException e) {
            log.error("ComposeGenerate  generateComposeConfig  method  execute fail {}  ",e.getMessage());
            schTaskInfoMapper.update(
                    new LambdaUpdateWrapper<SchTaskInfo>()
                            .eq(SchTaskInfo::getId, params.get("taskId"))
                            .set(SchTaskInfo::getFailReason, e.getMessage())
                            .set(SchTaskInfo::getUpdateTime, new Date())
                            .set(SchTaskInfo::getStatus, "failed")
            );
            log.error("Failed to generate Docker Compose configuration: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to generate Docker Compose configuration", e);
        }
    }


    /**
     * 递归构建目录树
     * @param path 文件路径 example: /home/<USER>/test/test1/test2
     * @param channelSftp 文件传输对象
     */

    private void circleMakeDirs(String path, ChannelSftp channelSftp) {
        String[] dirs = path.split("/");
        StringBuilder currentPath = new StringBuilder();
        for (String dir : dirs) {
            if (dir.isEmpty()) {
                continue;
            }
            currentPath.append("/").append(dir);
            try {
                channelSftp.mkdir(currentPath.toString());
            } catch (SftpException e) {
                if (e.id != ChannelSftp.SSH_FX_FAILURE) {
                    log.warn("Directory creation warning for path: {} - {}", currentPath, e.getMessage());
                }
            }
        }
    }


    /**
     * 提取容器名称
     * @param psOutput docker ps 输出
     * @return 容器名称列表
     */
    public List<String> extractContainerNames(String psOutput) {
        log.info("ComposeGenerate extractContainerNames method start");
        // TODO compose 文件有多个镜像 docker ps -a 多个镜像的名字no
        List<String> containerNames = new ArrayList<>();
        if (psOutput == null || psOutput.trim().isEmpty()) {
            return containerNames;
        }
        // 按行分割输出
        String[] lines = psOutput.split("\n");
        // 跳过标题行
        for (int i = 1; i < lines.length; i++) {
            String line = lines[i].trim();
            if (!line.isEmpty()) {
                // 使用正则表达式匹配容器名称
                // docker-compose ps输出格式通常为：名称 状态 端口等
                String[] parts = line.split("\\s+");
                if (parts.length > 0) {
                    log.info("获取到启动容器名.....containerName: {}",parts[0]);
                    containerNames.add(parts[0]);
                }
            }
        }
        return containerNames;
    }


    /**
     * 使用 docker stats 获取容器状态 数据信息采集
     * @param remoteHostId resourceId 用于获取远程主机信息
     * @param taskId 任务ID
     * @param params 参数 此处接收 computeDeviceId resourceId
     */
    public void containerMonitorCollect(Long remoteHostId,Long taskId,Map<String,Long> params) {
        Session session = null;
        ChannelExec channelExec = null;
        SchTaskInfo schTaskInfo = schTaskInfoMapper.selectById(taskId);
        SchResourceInfo info = schResourceInfoMapper.selectById(schTaskInfo.getResourceId());
        try {
            SshConnection connection = sshConnectionUtil.createConnection(info.getHostIp(), info.getPort(), info.getLoginName(), info.getPassword(), 30000);
            session = connection.getSession();
            channelExec = (ChannelExec) session.openChannel("exec");
            String statusCommand = "docker inspect --format='{{.State.Status}}' " + schTaskInfo.getContainerName();
            //  指定 容器名查询状态   只会有一行输出
          //  String monitorCommand = "docker stats --no-stream  "+schTaskInfo.getContainerName();
            String monitorCommand = String.format("docker stats --no-stream  %s",schTaskInfo.getContainerName());
            channelExec.setCommand( statusCommand);
            InputStream statusIn = channelExec.getInputStream();
            channelExec.connect();
            Thread.sleep(10000);
            String status = IOUtils.toString(statusIn, StandardCharsets.UTF_8).trim();
            log.info(" containerMonitorCollect Container {} status: {}", schTaskInfo.getContainerName(), status);
            List<DockerStats> dockerStats = null;
            if("running".equals(status)){
                channelExec.disconnect();
                ChannelExec exec = (ChannelExec) session.openChannel("exec");
                exec.setCommand(monitorCommand);
                InputStream inputStream = exec.getInputStream();
                exec.connect();
                Thread.sleep(50000);
                String monitorIo = IOUtils.toString(inputStream, StandardCharsets.UTF_8);
                // docker stats 数据信息收集
                dockerStats = this.paseContainerStatus(monitorIo, taskId);
            }else {
                // 启动失败 TODO 数据采集不跟新任务状态
                log.error(" ComposeGenerate containerMonitorCollect  docker stats is null ");
            }
            // 任务数据踩点
            if(CollectionUtils.isNotEmpty(dockerStats)){
                List<SchTaskMonitoring>  monitoringList = new ArrayList<>();
                // TODO 如果compose 是 多个容器收集为一条插入
                Timestamp ts=  new Timestamp(System.currentTimeMillis());
                for (DockerStats item : dockerStats) {
                    BigDecimal cpu = extractToBigDecimal(item.getCpuPercent());
                    BigDecimal memU = extractToBigDecimal(item.getMemUsage());
                    //  任务监控表
                    SchTaskMonitoring monitoring = new SchTaskMonitoring();
                    SchTaskMonitoring schTask= monitoring
                            .setId(idGeneratorWrapper.nextLongId())
                            .setTaskId(taskId)
                            .setComputeDeviceId(params.get("computeDeviceId"))
                            .setResourceId(params.get("resourceId"))
                            .setCreateTime(new Date())
                            .setCpuUsage(cpu)
                            .setMemoryUsage(memU)
                            .setTs(ts);
                    monitoringList.add(schTask);
                }
                schTaskMonitoringService.saveMonitoring(monitoringList.get(0));
            }

        } catch (Exception e) {
            log.error("Failed to get compose service status: {}", e.getMessage());
           throw new MyRuntimeException("获取服务状态失败: " + e.getMessage());
        } finally {
            if (channelExec != null && channelExec.isConnected()) {
                channelExec.disconnect();
            }
            if (session != null && session.isConnected()) {
                session.disconnect();
            }
        }
    }


    /**
     * 对 docker stats 的结果进行解析 将字符串转为 BigDecimal
     * @param input  解析好的 docker stats 当个输出结果
     * @return 转化为数字的结果
     */
    public  BigDecimal extractToBigDecimal(String input) {
        Matcher matcher = DIGIT_PATTERN.matcher(input);
        if (matcher.find()) {
            String numberStr = matcher.group(1);

            if (numberStr.endsWith("%")) {
                // 如果是百分比，去掉 % 后再转为 BigDecimal
                numberStr = numberStr.substring(0, numberStr.length() - 1);
            }

            return new BigDecimal(numberStr);
        }
        throw new IllegalArgumentException("未找到可解析的数字: " + input);
    }


    /**
     * doocker stats 数据信息解析
     * @param psOutput  ssh docker stats 结果
     * @param taskId 任务id
     * @return 解析结果
     */
    private  List<DockerStats>  paseContainerStatus(String psOutput,Long taskId) {
        if(Objects.isNull(psOutput) || psOutput.trim().isEmpty() ){
             log.error(" ComposeGenerate paseContainerStatus  docker stats result is null ");
             throw  new MyRuntimeException("任务启动失败");
        }
        List<DockerStats> statsList = new ArrayList<>();
        // 按行分割输出
        String[] lines = psOutput.split("\n");
        for (int i = 1; i < lines.length; i++) {
            String currentLine = lines[i].trim();
            Matcher matcher = DOCKER_STATS.matcher(currentLine);
            if (matcher.matches()) {
                DockerStats stats = new DockerStats();
                stats.setContainerId(matcher.group(1));
                stats.setName(matcher.group(2));
                stats.setCpuPercent(matcher.group(3));
                stats.setMemUsage(matcher.group(4));
                stats.setMemLimit(matcher.group(5));
                stats.setMemPercent(matcher.group(6));
                stats.setNetInput(matcher.group(7));
                stats.setNetOutput(matcher.group(8));
                stats.setBlockInput(matcher.group(9));
                stats.setBlockOutput(matcher.group(10));
                stats.setPids(matcher.group(11));
                statsList.add(stats);
            }
        }
        return statsList;
    }


    /**
     *  获取容器ID
     * @param session SSH 连接信息
     * @param containerName 容器名
     * @param params 参数 此处接收taskId
     * @return 容器id
     */
    public String getContainerId(Session session, String containerName,Map<String,Long> params) {
        log.info("ComposeGenerate  getContainerId  method  start 当前任务ID  {}",params.get("taskId"));
        ChannelExec channelExec = null;
        try {
            channelExec = (ChannelExec) session.openChannel("exec");
            String command = String.format("docker ps -a --filter name=%s --format '{{.ID}}'", containerName);
            channelExec.setCommand(command);
            // 获取命令输出
            InputStream in = channelExec.getInputStream();
            channelExec.connect();
            String result = IOUtils.toString(in, StandardCharsets.UTF_8).trim();
            log.info("Successfully retrieved container id for container {}: {}  任务ID  {}", containerName, result,params.get("taskId"));
            if (result.isEmpty()) {
                log.warn("No container found with name: {}", containerName);
                return null;
            }
            return result;
        } catch (Exception e) {
            log.error("ComposeGenerate  getContainerId  method  execute fail {}   当前任务id  {}",e.getMessage(),params.get("taskId"));
            schTaskInfoMapper.update(
                    new LambdaUpdateWrapper<SchTaskInfo>()
                            .eq(SchTaskInfo::getId, params.get("taskId"))
                            .set(SchTaskInfo::getFailReason, e.getMessage())
                            .set(SchTaskInfo::getUpdateTime, new Date())
                            .set(SchTaskInfo::getStatus, "failed")
            );
            log.error("Failed to get container id for {}: {} 任务id {}", containerName, e.getMessage(),params.get("taskId"));
            throw new MyRuntimeException("获取容器ID失败: " + e.getMessage());
        } finally {
            if (channelExec != null && channelExec.isConnected()) {
                channelExec.disconnect();
            }
            if (session != null && session.isConnected()) {
                session.disconnect();
            }
        }
    }
}
