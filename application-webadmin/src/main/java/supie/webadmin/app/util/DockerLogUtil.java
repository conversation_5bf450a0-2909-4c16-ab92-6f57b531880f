package supie.webadmin.app.util;

import com.jcraft.jsch.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import supie.common.core.exception.MyRuntimeException;

import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

/**
 * Docker日志获取工具类
 * 基于SSH连接获取Docker容器日志
 *
 * <AUTHOR>
 * @date 2024-06-01
 */
@Slf4j
@Component
public class DockerLogUtil {

    @Autowired
    private SshConnectionUtil sshConnectionUtil;

    /**
     * 获取Docker容器日志
     *
     * @param host 服务器IP
     * @param port SSH端口
     * @param username 用户名
     * @param password 密码
     * @param containerName 容器名称
     * @param lines 获取的日志行数，-1表示获取全部
     * @param timeout 连接超时时间（毫秒）
     * @return 容器日志内容
     */
    public String getDockerLogs(String host, int port, String username,
                                String password,
                               String containerName, int lines,
                                int timeout) {
        SshConnection connection = null;
        try {
            // 创建SSH连接
            connection = sshConnectionUtil.createConnection(host, port, username, password, timeout);
            // 构建docker logs命令
            String command = buildDockerLogsCommand(containerName, lines);
            // 执行命令获取日志
            return sshConnectionUtil.executeCommand(connection, command);
        } catch (JSchException e) {
            log.error("SSH连接失败: {}", e.getMessage(), e);
            throw new MyRuntimeException("SSH连接失败: " + e.getMessage());
        } catch (IOException e) {
            log.error("获取Docker日志失败: {}", e.getMessage(), e);
            throw new MyRuntimeException("获取Docker日志失败: " + e.getMessage());
        } finally {
            if (connection != null) {
                connection.close();
            }
        }
    }

    /**
     * 获取Docker容器日志（使用私钥认证）
     *
     * @param host 服务器IP
     * @param port SSH端口
     * @param username 用户名
     * @param privateKeyPath 私钥路径
     * @param containerName 容器名称
     * @param lines 获取的日志行数，-1表示获取全部
     * @param timeout 连接超时时间（毫秒）
     * @return 容器日志内容
     */
    public String getDockerLogsWithPrivateKey(String host, int port, String username, String privateKeyPath, 
                                            String containerName, int lines, int timeout) {
        SshConnection connection = null;
        try {
            // 创建SSH连接（私钥认证）
            connection = sshConnectionUtil.createConnectionWithPrivateKey(host, port, username, privateKeyPath, timeout);
            
            // 构建docker logs命令
            String command = buildDockerLogsCommand(containerName, lines);
            
            // 执行命令获取日志
            return sshConnectionUtil.executeCommand(connection, command);
        } catch (JSchException e) {
            log.error("SSH连接失败(私钥): {}", e.getMessage(), e);
            throw new MyRuntimeException("SSH连接失败(私钥): " + e.getMessage());
        } catch (IOException e) {
            log.error("获取Docker日志失败: {}", e.getMessage(), e);
            throw new MyRuntimeException("获取Docker日志失败: " + e.getMessage());
        } finally {
            if (connection != null) {
                connection.close();
            }
        }
    }

    /**
     * 获取Docker容器日志并按时间戳过滤
     *
     * @param host 服务器IP
     * @param port SSH端口
     * @param username 用户名
     * @param password 密码
     * @param containerName 容器名称
     * @param lines 获取的日志行数，-1表示获取全部
     * @param since 开始时间（Unix时间戳，单位：秒）
     * @param until 结束时间（Unix时间戳，单位：秒）
     * @param timeout 连接超时时间（毫秒）
     * @return 容器日志内容
     */
    public String getDockerLogsWithTimeFilter(String host, int port, String username, String password, 
                                            String containerName, int lines, String since, String until, int timeout) {
        SshConnection connection = null;
        try {
            // 创建SSH连接
            connection = sshConnectionUtil.createConnection(host, port, username, password, timeout);
            
            // 构建带时间过滤的docker logs命令
            String command = buildDockerLogsCommandWithTimeFilter(containerName, lines, since, until);
            
            // 执行命令获取日志
            return sshConnectionUtil.executeCommand(connection, command);
        } catch (JSchException e) {
            log.error("SSH连接失败: {}", e.getMessage(), e);
            throw new MyRuntimeException("SSH连接失败: " + e.getMessage());
        } catch (IOException e) {
            log.error("获取Docker日志失败: {}", e.getMessage(), e);
            throw new MyRuntimeException("获取Docker日志失败: " + e.getMessage());
        } finally {
            if (connection != null) {
                connection.close();
            }
        }
    }

    /**
     * 获取多个Docker容器的日志
     *
     * @param host 服务器IP
     * @param port SSH端口
     * @param username 用户名
     * @param password 密码
     * @param containerNames 容器名称列表
     * @param lines 每个容器获取的日志行数，-1表示获取全部
     * @param timeout 连接超时时间（毫秒）
     * @return 容器名称与日志内容的映射
     */
    public List<ContainerLog> getMultipleContainerLogs(String host, int port, String username, String password, 
                                                     List<String> containerNames, int lines, int timeout) {
        SshConnection connection = null;
        List<ContainerLog> containerLogs = new ArrayList<>();
        
        try {
            // 创建SSH连接
            connection = sshConnectionUtil.createConnection(host, port, username, password, timeout);
            
            // 获取每个容器的日志
            for (String containerName : containerNames) {
                try {
                    String command = buildDockerLogsCommand(containerName, lines);
                    String logs = sshConnectionUtil.executeCommand(connection, command);
                    
                    ContainerLog containerLog = new ContainerLog();
                    containerLog.setContainerName(containerName);
                    containerLog.setLogs(logs);
                    containerLogs.add(containerLog);
                } catch (Exception e) {
                    log.error("获取容器 {} 的日志失败: {}", containerName, e.getMessage());
                    ContainerLog errorLog = new ContainerLog();
                    errorLog.setContainerName(containerName);
                    errorLog.setLogs("获取日志失败: " + e.getMessage());
                    errorLog.setError(true);
                    containerLogs.add(errorLog);
                }
            }
            
            return containerLogs;
        } catch (JSchException e) {
            log.error("SSH连接失败: {}", e.getMessage(), e);
            throw new MyRuntimeException("SSH连接失败: " + e.getMessage());
        } finally {
            if (connection != null) {
                connection.close();
            }
        }
    }

    /**
     * 实时获取Docker容器日志（流式输出）
     * 
     * @param host 服务器IP
     * @param port SSH端口
     * @param username 用户名
     * @param password 密码
     * @param containerName 容器名称
     * @param timeout 连接超时时间（毫秒）
     * @param logHandler 日志处理回调接口
     */
    public void streamDockerLogs(String host, int port, String username, String password, 
                               String containerName, int timeout, LogHandler logHandler) {
        SshConnection connection = null;
        ChannelExec channel = null;
        
        try {
            // 创建SSH连接
            connection = sshConnectionUtil.createConnection(host, port, username, password, timeout);
            Session session = connection.getSession();
            
            // 创建执行通道
            channel = (ChannelExec) session.openChannel("exec");
            
            // 构建实时日志命令
            String command = "docker logs -f " + containerName;
            channel.setCommand(command);
            
            // 获取输入流
            InputStream inputStream = channel.getInputStream();
            InputStream errorStream = channel.getErrStream();
            
            // 连接通道
            channel.connect();
            
            // 创建日志读取线程
            Thread logReaderThread = new Thread(() -> {
                try {
                    byte[] buffer = new byte[1024];
                    int bytesRead;
                    
                    while ((bytesRead = inputStream.read(buffer)) != -1) {
                        String logChunk = new String(buffer, 0, bytesRead, StandardCharsets.UTF_8);
                        logHandler.handleLog(logChunk, false);
                    }
                } catch (IOException e) {
                    if (!e.getMessage().contains("Stream closed")) {
                        log.error("读取日志流失败: {}", e.getMessage());
                        logHandler.handleError(e);
                    }
                }
            });
            
            // 创建错误读取线程
            Thread errorReaderThread = new Thread(() -> {
                try {
                    byte[] buffer = new byte[1024];
                    int bytesRead;
                    
                    while ((bytesRead = errorStream.read(buffer)) != -1) {
                        String errorChunk = new String(buffer, 0, bytesRead, StandardCharsets.UTF_8);
                        logHandler.handleLog(errorChunk, true);
                    }
                } catch (IOException e) {
                    if (!e.getMessage().contains("Stream closed")) {
                        log.error("读取错误流失败: {}", e.getMessage());
                        logHandler.handleError(e);
                    }
                }
            });
            
            // 启动线程
            logReaderThread.start();
            errorReaderThread.start();
            
            // 等待线程完成
            logReaderThread.join();
            errorReaderThread.join();
            
        } catch (JSchException e) {
            log.error("SSH连接失败: {}", e.getMessage(), e);
            logHandler.handleError(e);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("日志读取线程被中断: {}", e.getMessage());
            logHandler.handleError(e);
        } catch (IOException e) {
            throw new RuntimeException(e);
        } finally {
            if (channel != null && channel.isConnected()) {
                channel.disconnect();
            }
            if (connection != null) {
                connection.close();
            }
        }
    }

    /**
     * 构建Docker日志命令
     *
     * @param containerName 容器名称
     * @param lines 获取的日志行数，-1表示获取全部
     * @return 构建的命令
     */
    private String buildDockerLogsCommand(String containerName, int lines) {
        StringBuilder command = new StringBuilder("docker logs");
        
        // 添加行数限制
        if (lines > 0) {
            command.append(" --tail ").append(lines);
        }
        
        // 添加时间戳
        command.append(" --timestamps");
        
        // 添加容器名称
        command.append(" ").append(containerName);
        
        return command.toString();
    }

    /**
     * 构建带时间过滤的Docker日志命令
     *
     * @param containerName 容器名称
     * @param lines 获取的日志行数，-1表示获取全部
     * @param since 开始时间（Unix时间戳或相对时间，如'1h'）
     * @param until 结束时间（Unix时间戳或相对时间，如'1h'）
     * @return 构建的命令
     */
    private String buildDockerLogsCommandWithTimeFilter(String containerName, int lines, String since, String until) {
        StringBuilder command = new StringBuilder("docker logs");
        
        // 添加行数限制
        if (lines > 0) {
            command.append(" --tail ").append(lines);
        }
        
        // 添加时间戳
        command.append(" --timestamps");
        
        // 添加开始时间
        if (since != null && !since.isEmpty()) {
            command.append(" --since ").append(since);
        }
        
        // 添加结束时间
        if (until != null && !until.isEmpty()) {
            command.append(" --until ").append(until);
        }
        
        // 添加容器名称
        command.append(" ").append(containerName);
        
        return command.toString();
    }

    /**
     * 容器日志数据类
     */
    public static class ContainerLog {
        private String containerName;
        private String logs;
        private boolean error = false;

        public String getContainerName() {
            return containerName;
        }

        public void setContainerName(String containerName) {
            this.containerName = containerName;
        }

        public String getLogs() {
            return logs;
        }

        public void setLogs(String logs) {
            this.logs = logs;
        }

        public boolean isError() {
            return error;
        }

        public void setError(boolean error) {
            this.error = error;
        }
    }

    /**
     * 日志处理回调接口
     */
    public interface LogHandler {
        /**
         * 处理日志块
         *
         * @param logChunk 日志内容块
         * @param isError 是否是错误日志
         */
        void handleLog(String logChunk, boolean isError);

        /**
         * 处理错误
         *
         * @param e 异常
         */
        void handleError(Exception e);
    }
}