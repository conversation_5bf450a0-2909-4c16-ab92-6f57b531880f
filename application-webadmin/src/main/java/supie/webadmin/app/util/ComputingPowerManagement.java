package supie.webadmin.app.util;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;

import lombok.Data;
import supie.common.core.util.MyCommonUtil;

import java.util.Date;

/**
 * ComputingPowerManagement实体对象。
 *
 * <AUTHOR> -rf .bug
 * @date 2024-01-02
 */
@Data
//@TableName(value = "lmd_service_computing_power_management")
public class ComputingPowerManagement {

    /**
     * 主键ID。
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 逻辑删除标记字段(1: 正常 -1: 已删除)。
     */
    @TableLogic
    private Integer isDelete;

    /**
     * 创建者ID。
     */
    private Long createUserId;

    /**
     * 创建时间。
     */
    private Date createTime;

    /**
     * 修改者ID。
     */
    private Long updateUserId;

    /**
     * 数据所属人ID。
     */
   // @UserFilterColumn
    private Long dataUserId;

    /**
     * 数据所属部门ID。
     */
    //@DeptFilterColumn
    private Long dataDeptId;

    /**
     * 服务器id。
     */
    private Long remoteHostId;


    /**
     * updateTime 。
     */
    private Date updateTime;


    /**
     * 备注。
     */
    private String computingPowerRemarks;

    /**
     * gpu序号。
     */
    private Integer gpu;

    /**
     * 所有通道的 NVLink 带宽计数器总数。
     */
    private Integer dcgmFiDevNvlinkBandwidthTotal;

    /**
     * UUID。
     */
    private String uuid;

    /**
     * 许可证状态。
     */
    private Integer dcgmFiDevVgpuLicenseStatus;

    /**
     * 设备。
     */
    private String device;

    /**
     * 节点名。
     */
    private String modelname;

    /**
     * 主机名。
     */
    private String hostname;

    /**
     * 设备ip。
     */
    private String deviceIp;


    /**
     * 是否使用。
     */
    @TableField(exist = false)
    private Integer isUsed;


    /**
     * 是否正在训练
     */
    @TableField(exist = false)
    private Integer isTrain;


    /**
     * createTime 范围过滤起始值(>=)。
     */
    @TableField(exist = false)
    private String createTimeStart;

    /**
     * createTime 范围过滤结束值(<=)。
     */
    @TableField(exist = false)
    private String createTimeEnd;


    /**
     * computing_power_remarks LIKE搜索字符串。
     */
    @TableField(exist = false)
    private String searchString;
    @TableField(exist = false)
    private Integer deviceState;
    @TableField(exist = false)
    private Object task;



    public void setSearchString(String searchString) {
        this.searchString = MyCommonUtil.replaceSqlWildcard(searchString);
    }

//    @Mapper
//    public interface ComputingPowerManagementModelMapper extends BaseModelMapper<ComputingPowerManagementVo, ComputingPowerManagement> {
//    }
//    public static final ComputingPowerManagementModelMapper INSTANCE = Mappers.getMapper(ComputingPowerManagementModelMapper.class);
}

