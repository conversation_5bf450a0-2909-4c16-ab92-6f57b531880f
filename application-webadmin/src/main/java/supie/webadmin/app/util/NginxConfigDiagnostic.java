package supie.webadmin.app.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import supie.common.core.exception.MyRuntimeException;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Nginx配置诊断和修复工具类
 * 
 * <AUTHOR> -rf .bug
 * @date 2025-06-20
 */
@Slf4j
@Component
public class NginxConfigDiagnostic {

    @Value("${nginx.mount-path}")
    private String nginxConfig;

    @Autowired
    private SshConnectionUtil sshConnectionUtil;

    /**
     * 诊断Nginx配置中的问题
     * 
     * @return 诊断结果列表
     */
    public List<String> diagnoseNginxConfig() {
        List<String> issues = new ArrayList<>();
        
        try {
            Path path = Paths.get(nginxConfig);
            if (!Files.exists(path)) {
                issues.add("Nginx配置文件不存在: " + nginxConfig);
                return issues;
            }
            
            List<String> lines = Files.readAllLines(path);
            
            // 检查重复的location块
            checkDuplicateLocations(lines, issues);
            
            // 检查无效的upstream主机
            checkInvalidUpstreams(lines, issues);
            
            // 检查语法错误
            checkSyntaxErrors(lines, issues);
            
        } catch (IOException e) {
            issues.add("读取Nginx配置文件失败: " + e.getMessage());
        }
        
        return issues;
    }

    /**
     * 检查重复的location块
     */
    private void checkDuplicateLocations(List<String> lines, List<String> issues) {
        Pattern locationPattern = Pattern.compile("location\\s+~\\s+\\^/([^/]+)/");
        List<String> foundPrefixes = new ArrayList<>();
        
        for (int i = 0; i < lines.size(); i++) {
            String line = lines.get(i).trim();
            Matcher matcher = locationPattern.matcher(line);
            
            if (matcher.find()) {
                String prefix = matcher.group(1);
                if (foundPrefixes.contains(prefix)) {
                    issues.add(String.format("第%d行: 发现重复的location块 '/%s/'", i + 1, prefix));
                } else {
                    foundPrefixes.add(prefix);
                }
            }
        }
    }

    /**
     * 检查无效的upstream主机
     */
    private void checkInvalidUpstreams(List<String> lines, List<String> issues) {
        Pattern proxyPassPattern = Pattern.compile("proxy_pass\\s+http://([^:]+):(\\d+);");
        
        for (int i = 0; i < lines.size(); i++) {
            String line = lines.get(i).trim();
            Matcher matcher = proxyPassPattern.matcher(line);
            
            if (matcher.find()) {
                String hostname = matcher.group(1);
                String port = matcher.group(2);
                
                // 检查主机名格式
                if (hostname.startsWith("code-") && hostname.length() > 20) {
                    // 这可能是一个任务ID格式的主机名
                    String taskId = hostname.substring(5); // 移除 "code-" 前缀
                    if (!isValidTaskId(taskId)) {
                        issues.add(String.format("第%d行: 可能无效的任务ID格式 '%s'", i + 1, hostname));
                    }
                }
            }
        }
    }

    /**
     * 检查基本语法错误
     */
    private void checkSyntaxErrors(List<String> lines, List<String> issues) {
        int braceCount = 0;
        
        for (int i = 0; i < lines.size(); i++) {
            String line = lines.get(i).trim();
            
            // 计算大括号
            for (char c : line.toCharArray()) {
                if (c == '{') braceCount++;
                if (c == '}') braceCount--;
            }
            
            // 检查负数的大括号计数
            if (braceCount < 0) {
                issues.add(String.format("第%d行: 大括号不匹配", i + 1));
                braceCount = 0; // 重置以继续检查
            }
        }
        
        if (braceCount != 0) {
            issues.add("配置文件大括号不匹配，缺少 " + Math.abs(braceCount) + " 个" + 
                      (braceCount > 0 ? "右" : "左") + "大括号");
        }
    }

    /**
     * 验证任务ID格式
     */
    private boolean isValidTaskId(String taskId) {
        // 任务ID应该是数字
        return taskId.matches("\\d+");
    }

    /**
     * 清理无效的location块
     * 
     * @param containerNames 当前存在的容器名称列表
     * @return 清理的location块数量
     */
    public int cleanupInvalidLocations(List<String> containerNames) {
        try {
            Path path = Paths.get(nginxConfig);
            List<String> lines = Files.readAllLines(path);
            List<String> cleanedLines = new ArrayList<>();
            
            int removedCount = 0;
            boolean inLocationBlock = false;
            List<String> currentLocationBlock = new ArrayList<>();
            String currentHostname = null;
            
            for (String line : lines) {
                String trimmedLine = line.trim();
                
                if (trimmedLine.matches("location\\s+~\\s+\\^/code\\d+/.*")) {
                    // 开始一个新的location块
                    inLocationBlock = true;
                    currentLocationBlock.clear();
                    currentLocationBlock.add(line);
                    
                    // 提取主机名
                    Pattern pattern = Pattern.compile("\\^/(code\\d+)/");
                    Matcher matcher = pattern.matcher(trimmedLine);
                    if (matcher.find()) {
                        currentHostname = matcher.group(1).replace("code", "code-");
                    }
                    
                } else if (inLocationBlock) {
                    currentLocationBlock.add(line);
                    
                    if (trimmedLine.equals("}")) {
                        // location块结束
                        inLocationBlock = false;
                        
                        // 检查容器是否存在
                        if (currentHostname != null && containerNames.contains(currentHostname)) {
                            // 容器存在，保留这个location块
                            cleanedLines.addAll(currentLocationBlock);
                        } else {
                            // 容器不存在，删除这个location块
                            removedCount++;
                            log.info("Removed invalid location block for container: {}", currentHostname);
                        }
                        
                        currentHostname = null;
                        currentLocationBlock.clear();
                    }
                } else {
                    // 不在location块中，直接添加
                    cleanedLines.add(line);
                }
            }
            
            // 写回文件
            if (removedCount > 0) {
                Files.write(path, cleanedLines);
                log.info("Cleaned up {} invalid location blocks from Nginx config", removedCount);
            }
            
            return removedCount;
            
        } catch (IOException e) {
            log.error("Failed to cleanup Nginx config: {}", e.getMessage());
            throw new MyRuntimeException("清理Nginx配置失败: " + e.getMessage());
        }
    }

    /**
     * 获取当前运行的容器列表
     * 
     * @param connection SSH连接
     * @return 容器名称列表
     */
    public List<String> getRunningContainers(SshConnection connection) {
        List<String> containers = new ArrayList<>();
        
        try {
            String result = sshConnectionUtil.executeCommand(connection, 
                "docker ps --filter name=code- --format '{{.Names}}'");
            
            if (result != null && !result.trim().isEmpty()) {
                String[] lines = result.trim().split("\n");
                for (String line : lines) {
                    String containerName = line.trim();
                    if (!containerName.isEmpty() && containerName.startsWith("code-")) {
                        containers.add(containerName);
                    }
                }
            }
            
        } catch (Exception e) {
            log.error("Failed to get running containers: {}", e.getMessage());
        }
        
        return containers;
    }

    /**
     * 格式化Nginx配置文件
     * 
     * @return 是否成功格式化
     */
    public boolean formatNginxConfig() {
        try {
            Path path = Paths.get(nginxConfig);
            List<String> lines = Files.readAllLines(path);
            List<String> formattedLines = new ArrayList<>();
            
            int indentLevel = 0;
            
            for (String line : lines) {
                String trimmedLine = line.trim();
                
                if (trimmedLine.isEmpty()) {
                    formattedLines.add("");
                    continue;
                }
                
                // 减少缩进（右大括号）
                if (trimmedLine.equals("}")) {
                    indentLevel = Math.max(0, indentLevel - 1);
                }
                
                // 添加适当的缩进
                StringBuilder formattedLine = new StringBuilder();
                for (int i = 0; i < indentLevel; i++) {
                    formattedLine.append("    ");
                }
                formattedLine.append(trimmedLine);
                formattedLines.add(formattedLine.toString());
                
                // 增加缩进（左大括号）
                if (trimmedLine.endsWith("{")) {
                    indentLevel++;
                }
            }
            
            Files.write(path, formattedLines);
            log.info("Nginx configuration formatted successfully");
            return true;
            
        } catch (IOException e) {
            log.error("Failed to format Nginx config: {}", e.getMessage());
            return false;
        }
    }
}
