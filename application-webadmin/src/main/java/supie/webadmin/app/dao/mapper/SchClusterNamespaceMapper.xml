<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="supie.webadmin.app.dao.SchClusterNamespaceMapper">
    <resultMap id="BaseResultMap" type="supie.webadmin.app.model.SchClusterNamespace">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="namespace_name" jdbcType="VARCHAR" property="namespaceName"/>
        <result column="namespace_description" jdbcType="VARCHAR" property="namespaceDescription"/>
        <result column="namespace_status" jdbcType="VARCHAR" property="namespaceStatus"/>
        <result column="namespace_run_time" jdbcType="VARCHAR" property="namespaceRunTime"/>
        <result column="str_id" jdbcType="VARCHAR" property="strId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="create_user_id" jdbcType="BIGINT" property="createUserId"/>
        <result column="update_user_id" jdbcType="BIGINT" property="updateUserId"/>
        <result column="data_user_id" jdbcType="BIGINT" property="dataUserId"/>
        <result column="data_dept_id" jdbcType="BIGINT" property="dataDeptId"/>
        <result column="is_delete" jdbcType="INTEGER" property="isDelete"/>
        <result column="status"  property="status"/>
    </resultMap>

    <insert id="insertList">
        INSERT INTO sch_cluster_namespace
            (id,
            namespace_name,
            namespace_description,
            namespace_status,
            namespace_run_time,
            str_id,
            update_time,
            create_time,
            create_user_id,
            update_user_id,
            data_user_id,
            data_dept_id,
            is_delete,
             status)
        VALUES
        <foreach collection="list" index="index" item="item" separator="," >
            (#{item.id},
            #{item.namespaceName},
            #{item.namespaceDescription},
            #{item.namespaceStatus},
            #{item.namespaceRunTime},
            #{item.strId},
            #{item.updateTime},
            #{item.createTime},
            #{item.createUserId},
            #{item.updateUserId},
            #{item.dataUserId},
            #{item.dataDeptId},
            #{item.isDelete},
             #{item.status}
            )
        </foreach>
    </insert>

    <!-- 如果有逻辑删除字段过滤，请写到这里 -->
    <sql id="filterRef">
        <!-- 这里必须加上全包名，否则当filterRef被其他Mapper.xml包含引用的时候，就会调用Mapper.xml中的该SQL片段 -->
        <include refid="supie.webadmin.app.dao.SchClusterNamespaceMapper.inputFilterRef"/>
        AND sch_cluster_namespace.is_delete = ${@supie.common.core.constant.GlobalDeletedFlag@NORMAL}
    </sql>

    <!-- 这里仅包含调用接口输入的主表过滤条件 -->
    <sql id="inputFilterRef">
        <if test="schClusterNamespaceFilter != null">
            <if test="schClusterNamespaceFilter.id != null">
                AND sch_cluster_namespace.id = #{schClusterNamespaceFilter.id}
            </if>
            <if test="schClusterNamespaceFilter.namespaceName != null and schClusterNamespaceFilter.namespaceName != ''">
                AND sch_cluster_namespace.namespace_name = #{schClusterNamespaceFilter.namespaceName}
            </if>
            <if test="schClusterNamespaceFilter.status != null and schClusterNamespaceFilter.status != ''">
                AND sch_cluster_namespace.status = #{schClusterNamespaceFilter.status}
            </if>
            <if test="schClusterNamespaceFilter.namespaceDescription != null and schClusterNamespaceFilter.namespaceDescription != ''">
                <bind name = "safeSchClusterNamespaceNamespaceDescription" value = "'%' + schClusterNamespaceFilter.namespaceDescription + '%'" />
                AND sch_cluster_namespace.namespace_description LIKE #{safeSchClusterNamespaceNamespaceDescription}
            </if>
            <if test="schClusterNamespaceFilter.namespaceStatus != null and schClusterNamespaceFilter.namespaceStatus != ''">
                <bind name = "safeSchClusterNamespaceNamespaceStatus" value = "'%' + schClusterNamespaceFilter.namespaceStatus + '%'" />
                AND sch_cluster_namespace.namespace_status LIKE #{safeSchClusterNamespaceNamespaceStatus}
            </if>
            <if test="schClusterNamespaceFilter.namespaceRunTime != null and schClusterNamespaceFilter.namespaceRunTime != ''">
                <bind name = "safeSchClusterNamespaceNamespaceRunTime" value = "'%' + schClusterNamespaceFilter.namespaceRunTime + '%'" />
                AND sch_cluster_namespace.namespace_run_time LIKE #{safeSchClusterNamespaceNamespaceRunTime}
            </if>
            <if test="schClusterNamespaceFilter.strId != null and schClusterNamespaceFilter.strId != ''">
                <bind name = "safeSchClusterNamespaceStrId" value = "'%' + schClusterNamespaceFilter.strId + '%'" />
                AND sch_cluster_namespace.str_id LIKE #{safeSchClusterNamespaceStrId}
            </if>
            <if test="schClusterNamespaceFilter.updateTimeStart != null and schClusterNamespaceFilter.updateTimeStart != ''">
                AND sch_cluster_namespace.update_time &gt;= #{schClusterNamespaceFilter.updateTimeStart}
            </if>
            <if test="schClusterNamespaceFilter.updateTimeEnd != null and schClusterNamespaceFilter.updateTimeEnd != ''">
                AND sch_cluster_namespace.update_time &lt;= #{schClusterNamespaceFilter.updateTimeEnd}
            </if>
            <if test="schClusterNamespaceFilter.createTimeStart != null and schClusterNamespaceFilter.createTimeStart != ''">
                AND sch_cluster_namespace.create_time &gt;= #{schClusterNamespaceFilter.createTimeStart}
            </if>
            <if test="schClusterNamespaceFilter.createTimeEnd != null and schClusterNamespaceFilter.createTimeEnd != ''">
                AND sch_cluster_namespace.create_time &lt;= #{schClusterNamespaceFilter.createTimeEnd}
            </if>
            <if test="schClusterNamespaceFilter.createUserId != null">
                AND sch_cluster_namespace.create_user_id = #{schClusterNamespaceFilter.createUserId}
            </if>
            <if test="schClusterNamespaceFilter.updateUserId != null">
                AND sch_cluster_namespace.update_user_id = #{schClusterNamespaceFilter.updateUserId}
            </if>
            <if test="schClusterNamespaceFilter.dataUserId != null">
                AND sch_cluster_namespace.data_user_id = #{schClusterNamespaceFilter.dataUserId}
            </if>
            <if test="schClusterNamespaceFilter.dataDeptId != null">
                AND sch_cluster_namespace.data_dept_id = #{schClusterNamespaceFilter.dataDeptId}
            </if>
            <if test="schClusterNamespaceFilter.isDelete != null">
                AND sch_cluster_namespace.is_delete = #{schClusterNamespaceFilter.isDelete}
            </if>
            <if test="schClusterNamespaceFilter.searchString != null and schClusterNamespaceFilter.searchString != ''">
                <bind name = "safeSchClusterNamespaceSearchString" value = "'%' + schClusterNamespaceFilter.searchString + '%'" />
                AND CONCAT(IFNULL(sch_cluster_namespace.namespace_name,''), IFNULL(sch_cluster_namespace.namespace_description,''), IFNULL(sch_cluster_namespace.namespace_status,'')) LIKE #{safeSchClusterNamespaceSearchString}
            </if>
        </if>
    </sql>

    <select id="getGroupedSchClusterNamespaceList" resultMap="BaseResultMap" parameterType="supie.webadmin.app.model.SchClusterNamespace">
        SELECT * FROM
            (SELECT
                COUNT(id) id,
                ${groupSelect}
            FROM sch_cluster_namespace
            <where>
                <include refid="filterRef"/>
            </where>
            GROUP BY ${groupBy}) sch_cluster_namespace
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>

    <select id="getSchClusterNamespaceList" resultMap="BaseResultMap" parameterType="supie.webadmin.app.model.SchClusterNamespace">
        SELECT * FROM sch_cluster_namespace
        <where>
            <include refid="filterRef"/>
        </where>
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>
</mapper>
