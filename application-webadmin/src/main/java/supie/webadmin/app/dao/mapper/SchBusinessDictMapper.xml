<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="supie.webadmin.app.dao.SchBusinessDictMapper">
    <resultMap id="BaseResultMap" type="supie.webadmin.app.model.SchBusinessDict">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="str_id" jdbcType="VARCHAR" property="strId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="create_user_id" jdbcType="BIGINT" property="createUserId"/>
        <result column="update_user_id" jdbcType="BIGINT" property="updateUserId"/>
        <result column="data_user_id" jdbcType="BIGINT" property="dataUserId"/>
        <result column="data_dept_id" jdbcType="BIGINT" property="dataDeptId"/>
        <result column="is_delete" jdbcType="INTEGER" property="isDelete"/>
        <result column="show_order" jdbcType="INTEGER" property="showOrder"/>
        <result column="parent_id" jdbcType="BIGINT" property="parentId"/>
        <result column="bind_type" jdbcType="VARCHAR" property="bindType"/>
        <result column="dict_name" jdbcType="VARCHAR" property="dictName"/>
        <result column="dict_description" jdbcType="VARCHAR" property="dictDescription"/>
        <result column="color_data" jdbcType="VARCHAR" property="colorData"/>
        <result column="other_data" jdbcType="VARCHAR" property="otherData"/>
        <result column="dict_level" jdbcType="INTEGER" property="dictLevel"/>
    </resultMap>

    <insert id="insertList">
        INSERT INTO sch_business_dict
            (id,
            str_id,
            update_time,
            create_time,
            create_user_id,
            update_user_id,
            data_user_id,
            data_dept_id,
            is_delete,
            show_order,
            parent_id,
            bind_type,
            dict_name,
            dict_description,
            color_data,
            other_data,
            dict_level)
        VALUES
        <foreach collection="list" index="index" item="item" separator="," >
            (#{item.id},
            #{item.strId},
            #{item.updateTime},
            #{item.createTime},
            #{item.createUserId},
            #{item.updateUserId},
            #{item.dataUserId},
            #{item.dataDeptId},
            #{item.isDelete},
            #{item.showOrder},
            #{item.parentId},
            #{item.bindType},
            #{item.dictName},
            #{item.dictDescription},
            #{item.colorData},
            #{item.otherData},
            #{item.dictLevel})
        </foreach>
    </insert>

    <!-- 如果有逻辑删除字段过滤，请写到这里 -->
    <sql id="filterRef">
        <!-- 这里必须加上全包名，否则当filterRef被其他Mapper.xml包含引用的时候，就会调用Mapper.xml中的该SQL片段 -->
        <include refid="supie.webadmin.app.dao.SchBusinessDictMapper.inputFilterRef"/>
        AND sch_business_dict.is_delete = ${@supie.common.core.constant.GlobalDeletedFlag@NORMAL}
    </sql>

    <!-- 这里仅包含调用接口输入的主表过滤条件 -->
    <sql id="inputFilterRef">
        <if test="schBusinessDictFilter != null">
            <if test="schBusinessDictFilter.id != null">
                AND sch_business_dict.id = #{schBusinessDictFilter.id}
            </if>
            <if test="schBusinessDictFilter.strId != null and schBusinessDictFilter.strId != ''">
                <bind name = "safeSchBusinessDictStrId" value = "'%' + schBusinessDictFilter.strId + '%'" />
                AND sch_business_dict.str_id LIKE #{safeSchBusinessDictStrId}
            </if>
            <if test="schBusinessDictFilter.updateTimeStart != null and schBusinessDictFilter.updateTimeStart != ''">
                AND sch_business_dict.update_time &gt;= #{schBusinessDictFilter.updateTimeStart}
            </if>
            <if test="schBusinessDictFilter.updateTimeEnd != null and schBusinessDictFilter.updateTimeEnd != ''">
                AND sch_business_dict.update_time &lt;= #{schBusinessDictFilter.updateTimeEnd}
            </if>
            <if test="schBusinessDictFilter.createTimeStart != null and schBusinessDictFilter.createTimeStart != ''">
                AND sch_business_dict.create_time &gt;= #{schBusinessDictFilter.createTimeStart}
            </if>
            <if test="schBusinessDictFilter.createTimeEnd != null and schBusinessDictFilter.createTimeEnd != ''">
                AND sch_business_dict.create_time &lt;= #{schBusinessDictFilter.createTimeEnd}
            </if>
            <if test="schBusinessDictFilter.createUserId != null">
                AND sch_business_dict.create_user_id = #{schBusinessDictFilter.createUserId}
            </if>
            <if test="schBusinessDictFilter.updateUserId != null">
                AND sch_business_dict.update_user_id = #{schBusinessDictFilter.updateUserId}
            </if>
            <if test="schBusinessDictFilter.dataUserId != null">
                AND sch_business_dict.data_user_id = #{schBusinessDictFilter.dataUserId}
            </if>
            <if test="schBusinessDictFilter.dataDeptId != null">
                AND sch_business_dict.data_dept_id = #{schBusinessDictFilter.dataDeptId}
            </if>
            <if test="schBusinessDictFilter.isDelete != null">
                AND sch_business_dict.is_delete = #{schBusinessDictFilter.isDelete}
            </if>
            <if test="schBusinessDictFilter.showOrder != null">
                AND sch_business_dict.show_order = #{schBusinessDictFilter.showOrder}
            </if>
            <if test="schBusinessDictFilter.parentId != null">
                AND sch_business_dict.parent_id = #{schBusinessDictFilter.parentId}
            </if>
            <if test="schBusinessDictFilter.bindType != null and schBusinessDictFilter.bindType != ''">
                <bind name = "safeSchBusinessDictBindType" value = "'%' + schBusinessDictFilter.bindType + '%'" />
                AND sch_business_dict.bind_type LIKE #{safeSchBusinessDictBindType}
            </if>
            <if test="schBusinessDictFilter.dictName != null and schBusinessDictFilter.dictName != ''">
                <bind name = "safeSchBusinessDictDictName" value = "'%' + schBusinessDictFilter.dictName + '%'" />
                AND sch_business_dict.dict_name LIKE #{safeSchBusinessDictDictName}
            </if>
            <if test="schBusinessDictFilter.dictDescription != null and schBusinessDictFilter.dictDescription != ''">
                <bind name = "safeSchBusinessDictDictDescription" value = "'%' + schBusinessDictFilter.dictDescription + '%'" />
                AND sch_business_dict.dict_description LIKE #{safeSchBusinessDictDictDescription}
            </if>
            <if test="schBusinessDictFilter.colorData != null and schBusinessDictFilter.colorData != ''">
                <bind name = "safeSchBusinessDictColorData" value = "'%' + schBusinessDictFilter.colorData + '%'" />
                AND sch_business_dict.color_data LIKE #{safeSchBusinessDictColorData}
            </if>
            <if test="schBusinessDictFilter.otherData != null and schBusinessDictFilter.otherData != ''">
                AND sch_business_dict.other_data = #{schBusinessDictFilter.otherData}
            </if>
            <if test="schBusinessDictFilter.dictLevel != null">
                AND sch_business_dict.dict_level = #{schBusinessDictFilter.dictLevel}
            </if>
            <if test="schBusinessDictFilter.searchString != null and schBusinessDictFilter.searchString != ''">
                <bind name = "safeSchBusinessDictSearchString" value = "'%' + schBusinessDictFilter.searchString + '%'" />
                AND CONCAT(IFNULL(sch_business_dict.str_id,''), IFNULL(sch_business_dict.bind_type,''), IFNULL(sch_business_dict.dict_name,''), IFNULL(sch_business_dict.dict_description,''), IFNULL(sch_business_dict.color_data,''), IFNULL(sch_business_dict.other_data,'')) LIKE #{safeSchBusinessDictSearchString}
            </if>
        </if>
    </sql>

    <select id="getGroupedSchBusinessDictList" resultMap="BaseResultMap" parameterType="supie.webadmin.app.model.SchBusinessDict">
        SELECT * FROM
            (SELECT
                COUNT(create_user_id) create_user_id,
                ${groupSelect}
            FROM sch_business_dict
            <where>
                <include refid="filterRef"/>
            </where>
            GROUP BY ${groupBy}) sch_business_dict
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>

    <select id="getSchBusinessDictList" resultMap="BaseResultMap" parameterType="supie.webadmin.app.model.SchBusinessDict">
        SELECT * FROM sch_business_dict
        <where>
            <include refid="filterRef"/>
        </where>
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>
</mapper>
