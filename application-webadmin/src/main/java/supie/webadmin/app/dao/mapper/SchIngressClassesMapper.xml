<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="supie.webadmin.app.dao.SchIngressClassesMapper">
    <resultMap id="BaseResultMap" type="supie.webadmin.app.model.SchIngressClasses">
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="ingress_class_name" jdbcType="VARCHAR" property="ingressClassName"/>
        <result column="controller" jdbcType="VARCHAR" property="controller"/>
        <result column="description" jdbcType="LONGVARCHAR" property="description"/>
        <result column="str_id" jdbcType="VARCHAR" property="strId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="create_user_id" jdbcType="BIGINT" property="createUserId"/>
        <result column="update_user_id" jdbcType="BIGINT" property="updateUserId"/>
        <result column="data_user_id" jdbcType="BIGINT" property="dataUserId"/>
        <result column="data_dept_id" jdbcType="BIGINT" property="dataDeptId"/>
        <result column="is_delete" jdbcType="INTEGER" property="isDelete"/>
    </resultMap>

    <insert id="insertList">
        INSERT INTO sch_ingress_classes
            (id,
            ingress_class_name,
            controller,
            description,
            str_id,
            update_time,
            create_time,
            create_user_id,
            update_user_id,
            data_user_id,
            data_dept_id,
            is_delete)
        VALUES
        <foreach collection="list" index="index" item="item" separator="," >
            (#{item.id},
            #{item.ingressClassName},
            #{item.controller},
            #{item.description},
            #{item.strId},
            #{item.updateTime},
            #{item.createTime},
            #{item.createUserId},
            #{item.updateUserId},
            #{item.dataUserId},
            #{item.dataDeptId},
            #{item.isDelete})
        </foreach>
    </insert>

    <!-- 如果有逻辑删除字段过滤，请写到这里 -->
    <sql id="filterRef">
        <!-- 这里必须加上全包名，否则当filterRef被其他Mapper.xml包含引用的时候，就会调用Mapper.xml中的该SQL片段 -->
        <include refid="supie.webadmin.app.dao.SchIngressClassesMapper.inputFilterRef"/>
        AND sch_ingress_classes.is_delete = ${@supie.common.core.constant.GlobalDeletedFlag@NORMAL}
    </sql>

    <!-- 这里仅包含调用接口输入的主表过滤条件 -->
    <sql id="inputFilterRef">
        <if test="schIngressClassesFilter != null">
            <if test="schIngressClassesFilter.id != null and schIngressClassesFilter.id != ''">
                <bind name = "safeSchIngressClassesId" value = "'%' + schIngressClassesFilter.id + '%'" />
                AND sch_ingress_classes.id LIKE #{safeSchIngressClassesId}
            </if>
            <if test="schIngressClassesFilter.ingressClassName != null and schIngressClassesFilter.ingressClassName != ''">
                <bind name = "safeSchIngressClassesIngressClassName" value = "'%' + schIngressClassesFilter.ingressClassName + '%'" />
                AND sch_ingress_classes.ingress_class_name LIKE #{safeSchIngressClassesIngressClassName}
            </if>
            <if test="schIngressClassesFilter.controller != null and schIngressClassesFilter.controller != ''">
                <bind name = "safeSchIngressClassesController" value = "'%' + schIngressClassesFilter.controller + '%'" />
                AND sch_ingress_classes.controller LIKE #{safeSchIngressClassesController}
            </if>
            <if test="schIngressClassesFilter.description != null and schIngressClassesFilter.description != ''">
                <bind name = "safeSchIngressClassesDescription" value = "'%' + schIngressClassesFilter.description + '%'" />
                AND sch_ingress_classes.description LIKE #{safeSchIngressClassesDescription}
            </if>
            <if test="schIngressClassesFilter.strId != null and schIngressClassesFilter.strId != ''">
                <bind name = "safeSchIngressClassesStrId" value = "'%' + schIngressClassesFilter.strId + '%'" />
                AND sch_ingress_classes.str_id LIKE #{safeSchIngressClassesStrId}
            </if>
            <if test="schIngressClassesFilter.updateTimeStart != null and schIngressClassesFilter.updateTimeStart != ''">
                AND sch_ingress_classes.update_time &gt;= #{schIngressClassesFilter.updateTimeStart}
            </if>
            <if test="schIngressClassesFilter.updateTimeEnd != null and schIngressClassesFilter.updateTimeEnd != ''">
                AND sch_ingress_classes.update_time &lt;= #{schIngressClassesFilter.updateTimeEnd}
            </if>
            <if test="schIngressClassesFilter.createTimeStart != null and schIngressClassesFilter.createTimeStart != ''">
                AND sch_ingress_classes.create_time &gt;= #{schIngressClassesFilter.createTimeStart}
            </if>
            <if test="schIngressClassesFilter.createTimeEnd != null and schIngressClassesFilter.createTimeEnd != ''">
                AND sch_ingress_classes.create_time &lt;= #{schIngressClassesFilter.createTimeEnd}
            </if>
            <if test="schIngressClassesFilter.createUserId != null">
                AND sch_ingress_classes.create_user_id = #{schIngressClassesFilter.createUserId}
            </if>
            <if test="schIngressClassesFilter.updateUserId != null">
                AND sch_ingress_classes.update_user_id = #{schIngressClassesFilter.updateUserId}
            </if>
            <if test="schIngressClassesFilter.dataUserId != null">
                AND sch_ingress_classes.data_user_id = #{schIngressClassesFilter.dataUserId}
            </if>
            <if test="schIngressClassesFilter.dataDeptId != null">
                AND sch_ingress_classes.data_dept_id = #{schIngressClassesFilter.dataDeptId}
            </if>
            <if test="schIngressClassesFilter.isDelete != null">
                AND sch_ingress_classes.is_delete = #{schIngressClassesFilter.isDelete}
            </if>
            <if test="schIngressClassesFilter.searchString != null and schIngressClassesFilter.searchString != ''">
                <bind name = "safeSchIngressClassesSearchString" value = "'%' + schIngressClassesFilter.searchString + '%'" />
                AND CONCAT(IFNULL(sch_ingress_classes.id,''), IFNULL(sch_ingress_classes.ingress_class_name,''), IFNULL(sch_ingress_classes.controller,''), IFNULL(sch_ingress_classes.description,'')) LIKE #{safeSchIngressClassesSearchString}
            </if>
        </if>
    </sql>

    <select id="getGroupedSchIngressClassesList" resultMap="BaseResultMap" parameterType="supie.webadmin.app.model.SchIngressClasses">
        SELECT * FROM
            (SELECT
                COUNT(create_user_id) create_user_id,
                ${groupSelect}
            FROM sch_ingress_classes
            <where>
                <include refid="filterRef"/>
            </where>
            GROUP BY ${groupBy}) sch_ingress_classes
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>

    <select id="getSchIngressClassesList" resultMap="BaseResultMap" parameterType="supie.webadmin.app.model.SchIngressClasses">
        SELECT * FROM sch_ingress_classes
        <where>
            <include refid="filterRef"/>
        </where>
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>
</mapper>
