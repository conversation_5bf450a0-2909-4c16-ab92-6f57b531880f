<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="supie.webadmin.app.dao.SchCardMonitorMapper">
    <resultMap id="BaseResultMap" type="supie.webadmin.app.model.SchCardMonitor">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="resource_id" jdbcType="BIGINT" property="resourceId"/>
        <result column="compute_device_id" jdbcType="BIGINT" property="computeDeviceId"/>
        <result column="card_utilization" jdbcType="DECIMAL" property="cardUtilization"/>
        <result column="monitor_type" jdbcType="TINYINT" property="monitorType"/>
        <result column="create_user_id" jdbcType="BIGINT" property="createUserId"/>
        <result column="update_user_id" jdbcType="BIGINT" property="updateUserId"/>
        <result column="data_user_id" jdbcType="BIGINT" property="dataUserId"/>
        <result column="data_dept_id" jdbcType="BIGINT" property="dataDeptId"/>
        <result column="is_delete" jdbcType="INTEGER" property="isDelete"/>
        <result column="hb_total" jdbcType="VARCHAR" property="hbTotal"/>
        <result column="hb_util" jdbcType="DOUBLE" property="hbUtil"/>
        <result column="hb_used" jdbcType="VARCHAR" property="hbUsed"/>
        <result column="memory_total" jdbcType="BIGINT" property="memoryTotal"/>
        <result column="memory_used" jdbcType="BIGINT" property="memoryUsed"/>
        <result column="memory_util" jdbcType="DECIMAL" property="memoryUtil"/>
        <result column="str_id" jdbcType="VARCHAR" property="strId"/>
        <result column="ts" jdbcType="TIMESTAMP" property="ts"/>
        <result column="serial_number" jdbcType="INTEGER" property="serialNumber"/>
        <result column="temp" jdbcType="INTEGER" property="temp"/>
        <result column="fb_free" jdbcType="BIGINT" property="fbFree"/>
        <result column="fb_used" jdbcType="BIGINT" property="fbUsed"/>
    </resultMap>

    <insert id="insertList">
        INSERT INTO sch_card_monitor
        (id,
        resource_id,
        compute_device_id,
        card_utilization,
        monitor_type,
        create_user_id,
        update_user_id,
        data_user_id,
        data_dept_id,
        is_delete,
        hb_total,
        hb_util,
        hb_used,
        memory_total,
        memory_used,
        memory_util,
        str_id,
        ts,
        serial_number,
        temp,
        fb_free,
        fb_used )
        VALUES
        <foreach collection="list" index="index" item="item" separator=",">
            (#{item.id},
            #{item.resourceId},
            #{item.computeDeviceId},
            #{item.cardUtilization},
            #{item.monitorType},
            #{item.createUserId},
            #{item.updateUserId},
            #{item.dataUserId},
            #{item.dataDeptId},
            #{item.isDelete},
            #{item.hbTotal},
            #{item.hbUtil},
            #{item.hbUsed},
            #{item.memoryTotal},
            #{item.memoryUsed},
            #{item.memoryUtil},
            #{item.strId},
            #{item.ts},
            #{item.serialNumber},
            #{item.temp},
            #{item.fbFree},
            #{item.fbUsed})
        </foreach>
    </insert>

    <!-- 如果有逻辑删除字段过滤，请写到这里 -->
    <sql id="filterRef">
        <!-- 这里必须加上全包名，否则当filterRef被其他Mapper.xml包含引用的时候，就会调用Mapper.xml中的该SQL片段 -->
        <include refid="supie.webadmin.app.dao.SchCardMonitorMapper.inputFilterRef"/>
        AND sch_card_monitor.is_delete = ${@supie.common.core.constant.GlobalDeletedFlag@NORMAL}
    </sql>

    <!-- 这里仅包含调用接口输入的主表过滤条件 -->
    <sql id="inputFilterRef">
        <if test="schCardMonitorFilter != null">
            <if test="schCardMonitorFilter.id != null">
                AND sch_card_monitor.id = #{schCardMonitorFilter.id}
            </if>
            <if test="schCardMonitorFilter.resourceId != null">
                AND sch_card_monitor.resource_id = #{schCardMonitorFilter.resourceId}
            </if>
            <if test="schCardMonitorFilter.cardUtilization != null">
                AND sch_card_monitor.card_utilization = #{schCardMonitorFilter.cardUtilization}
            </if>
            <if test="schCardMonitorFilter.monitorType != null">
                AND sch_card_monitor.monitor_type = #{schCardMonitorFilter.monitorType}
            </if>
            <if test="schCardMonitorFilter.createUserId != null">
                AND sch_card_monitor.create_user_id = #{schCardMonitorFilter.createUserId}
            </if>
            <if test="schCardMonitorFilter.updateUserId != null">
                AND sch_card_monitor.update_user_id = #{schCardMonitorFilter.updateUserId}
            </if>
            <if test="schCardMonitorFilter.dataUserId != null">
                AND sch_card_monitor.data_user_id = #{schCardMonitorFilter.dataUserId}
            </if>
            <if test="schCardMonitorFilter.dataDeptId != null">
                AND sch_card_monitor.data_dept_id = #{schCardMonitorFilter.dataDeptId}
            </if>
            <if test="schCardMonitorFilter.strId != null and schCardMonitorFilter.strId != ''">
                <bind name = "safeSchCardMonitorStrId" value = "'%' + schCardMonitorFilter.strId + '%'" />
                AND sch_card_monitor.str_id LIKE #{safeSchCardMonitorStrId}
            </if>
            <if test="schCardMonitorFilter.ts != null and schCardMonitorFilter.ts != ''">
                AND sch_card_monitor.ts = #{schCardMonitorFilter.ts}
            </if>
            <if test="schCardMonitorFilter.serialNumber != null and schCardMonitorFilter.serialNumber != ''">
                AND sch_card_monitor.serial_number = #{schCardMonitorFilter.serialNumber}
            </if>
            <if test="schCardMonitorFilter.searchString != null and schCardMonitorFilter.searchString != ''">
                <bind name = "safeSchCardMonitorSearchString" value = "'%' + schCardMonitorFilter.searchString + '%'" />
                AND IFNULL(sch_card_monitor.str_id,'') LIKE #{safeSchCardMonitorSearchString}
            </if>
        </if>
    </sql>

    <select id="getGroupedSchCardMonitorList" resultMap="BaseResultMap" parameterType="supie.webadmin.app.model.SchCardMonitor">
        SELECT * FROM
            (SELECT
                COUNT(id) id,
                ${groupSelect}
            FROM sch_card_monitor
            <where>
                <include refid="filterRef"/>
            </where>
            GROUP BY ${groupBy}) sch_card_monitor
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>

    <select id="getSchCardMonitorList" resultMap="BaseResultMap" parameterType="supie.webadmin.app.model.SchCardMonitor">
        SELECT * FROM sch_card_monitor
        <where>
            <include refid="filterRef"/>
        </where>
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>
    <select id="getNpuMonitoringByComputeDeviceId" resultMap="BaseResultMap" parameterType="supie.webadmin.app.model.SchCardMonitor">
        SELECT *
        FROM sch_card_monitor
        <where>
            <if test="computeDeviceId != null ">
                AND compute_device_id  =#{computeDeviceId}
            </if>
            <if test="ts != null ">
                AND ts = #{ts}
            </if>
        </where>
        ORDER BY ts DESC
    </select>

    <select id="statisticalIndicators" resultType="supie.webadmin.app.model.SchCardMonitor">
        SELECT ts AS tsLocal,*
        FROM
        sch_card_monitor
        <where>
            AND resource_id in #{resourceIdList}
            <if test="schCardMonitorFilter.tsStart != null and schCardMonitorFilter.tsStart != ''">
                AND ts &gt;= #{schCardMonitorFilter.tsStart}
            </if>
            <if test="schCardMonitorFilter.tsEnd != null and schCardMonitorFilter.tsEnd != ''">
                AND ts &lt;= #{schCardMonitorFilter.tsEnd}
            </if>
        </where>
        ORDER BY
        ts Asc
    </select>

    <select id="getSchCardMonitorListByIdList"
            resultType="supie.webadmin.app.model.SchCardMonitor">
        SELECT t.*
        FROM sch_card_monitor t
        JOIN (
        SELECT
        resource_id,
        max(ts) AS latest_ts
        FROM sch_card_monitor
        WHERE resource_id IN
        <foreach collection="schResourceInfoIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        GROUP BY resource_id
        ) m
        ON t.resource_id = m.resource_id AND t.ts = m.latest_ts;
    </select>
    <select id="getNpuMonitoringByResourceInfoId" resultMap="BaseResultMap" parameterType="supie.webadmin.app.model.SchCardMonitor">
        SELECT *
        FROM sch_card_monitor
        <where>
            <if test="resourceId != null ">
                AND resource_id  =#{resourceId}
            </if>
            <if test="ts != null ">
                AND ts = #{ts}
            </if>
        </where>
        ORDER BY ts DESC
    </select>
    <select id="getSchCardMonitorListByids" resultType="supie.webadmin.app.model.SchCardMonitor">
        SELECT t.*
        FROM sch_card_monitor t
        WHERE t.id IN (
        SELECT MAX(id)
        FROM sch_card_monitor
        WHERE compute_device_id IN
        <foreach collection="computeDeviceIds" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        GROUP BY compute_device_id
        )
    </select>
    <select id="getSchCardMonitorListByResourceId" resultType="supie.webadmin.app.model.SchCardMonitor">
        SELECT t.*
        FROM sch_card_monitor t
                 JOIN (
            SELECT
                resource_id,
                MAX(ts) AS latest_ts
            FROM sch_card_monitor
            WHERE resource_id = #{resourceId}
            GROUP BY resource_id
        ) m ON t.resource_id = m.resource_id AND t.ts = m.latest_ts
    </select>
</mapper>
