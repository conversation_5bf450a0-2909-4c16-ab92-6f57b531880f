<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="supie.webadmin.app.dao.SchTaskMonitoringMapper">
    <resultMap id="BaseResultMap" type="supie.webadmin.app.model.SchTaskMonitoring">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="str_id" jdbcType="VARCHAR" property="strId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="create_user_id" jdbcType="BIGINT" property="createUserId"/>
        <result column="update_user_id" jdbcType="BIGINT" property="updateUserId"/>
        <result column="data_user_id" jdbcType="BIGINT" property="dataUserId"/>
        <result column="data_dept_id" jdbcType="BIGINT" property="dataDeptId"/>
        <result column="is_delete" jdbcType="INTEGER" property="isDelete"/>
        <result column="task_id" jdbcType="BIGINT" property="taskId"/>
        <result column="compute_device_id" jdbcType="BIGINT" property="computeDeviceId"/>
        <result column="resource_id" jdbcType="BIGINT" property="resourceId"/>
        <result column="cpu_usage" jdbcType="DECIMAL" property="cpuUsage"/>
        <result column="memory_usage" jdbcType="VARCHAR" property="memoryUsage"/>
        <result column="graphics_memory_usage" jdbcType="VARCHAR" property="graphicsMemoryUsage"/>
        <result column="ts" jdbcType="TIMESTAMP" property="ts"/>
        <result column="container_id" jdbcType="BIGINT" property="containerId"/>
    </resultMap>

    <insert id="insertList">
        INSERT INTO sch_task_monitoring
            (id,
            str_id,
            update_time,
            create_time,
            create_user_id,
            update_user_id,
            data_user_id,
            data_dept_id,
            is_delete,
            task_id,
            compute_device_id,
            resource_id,
            cpu_usage,
            memory_usage,
            graphics_memory_usage,
            ts,
        container_id)
        VALUES
        <foreach collection="list" index="index" item="item" separator="," >
            (#{item.id},
            #{item.strId},
            #{item.updateTime},
            #{item.createTime},
            #{item.createUserId},
            #{item.updateUserId},
            #{item.dataUserId},
            #{item.dataDeptId},
            #{item.isDelete},
            #{item.taskId},
            #{item.computeDeviceId},
            #{item.resourceId},
            #{item.cpuUsage},
            #{item.memoryUsage},
            #{item.graphicsMemoryUsage},
            #{item.ts},
            #{item.containerId})
        </foreach>
    </insert>

    <!-- 如果有逻辑删除字段过滤，请写到这里 -->
    <sql id="filterRef">
        <!-- 这里必须加上全包名，否则当filterRef被其他Mapper.xml包含引用的时候，就会调用Mapper.xml中的该SQL片段 -->
        <include refid="supie.webadmin.app.dao.SchTaskMonitoringMapper.inputFilterRef"/>
        AND sch_task_monitoring.is_delete = ${@supie.common.core.constant.GlobalDeletedFlag@NORMAL}
    </sql>

    <!-- 这里仅包含调用接口输入的主表过滤条件 -->
    <sql id="inputFilterRef">
        <if test="schTaskMonitoringFilter != null">
            <if test="schTaskMonitoringFilter.id != null">
                AND sch_task_monitoring.id = #{schTaskMonitoringFilter.id}
            </if>
            <if test="schTaskMonitoringFilter.strId != null and schTaskMonitoringFilter.strId != ''">
                <bind name = "safeSchTaskMonitoringStrId" value = "'%' + schTaskMonitoringFilter.strId + '%'" />
                AND sch_task_monitoring.str_id LIKE #{safeSchTaskMonitoringStrId}
            </if>
            <if test="schTaskMonitoringFilter.updateTimeStart != null and schTaskMonitoringFilter.updateTimeStart != ''">
                AND sch_task_monitoring.update_time &gt;= #{schTaskMonitoringFilter.updateTimeStart}
            </if>
            <if test="schTaskMonitoringFilter.updateTimeEnd != null and schTaskMonitoringFilter.updateTimeEnd != ''">
                AND sch_task_monitoring.update_time &lt;= #{schTaskMonitoringFilter.updateTimeEnd}
            </if>
            <if test="schTaskMonitoringFilter.createTimeStart != null and schTaskMonitoringFilter.createTimeStart != ''">
                AND sch_task_monitoring.create_time &gt;= #{schTaskMonitoringFilter.createTimeStart}
            </if>
            <if test="schTaskMonitoringFilter.createTimeEnd != null and schTaskMonitoringFilter.createTimeEnd != ''">
                AND sch_task_monitoring.create_time &lt;= #{schTaskMonitoringFilter.createTimeEnd}
            </if>
            <if test="schTaskMonitoringFilter.createUserId != null">
                AND sch_task_monitoring.create_user_id = #{schTaskMonitoringFilter.createUserId}
            </if>
            <if test="schTaskMonitoringFilter.updateUserId != null">
                AND sch_task_monitoring.update_user_id = #{schTaskMonitoringFilter.updateUserId}
            </if>
            <if test="schTaskMonitoringFilter.dataUserId != null">
                AND sch_task_monitoring.data_user_id = #{schTaskMonitoringFilter.dataUserId}
            </if>
            <if test="schTaskMonitoringFilter.dataDeptId != null">
                AND sch_task_monitoring.data_dept_id = #{schTaskMonitoringFilter.dataDeptId}
            </if>
            <if test="schTaskMonitoringFilter.isDelete != null">
                AND sch_task_monitoring.is_delete = #{schTaskMonitoringFilter.isDelete}
            </if>
            <if test="schTaskMonitoringFilter.taskId != null">
                AND sch_task_monitoring.task_id = #{schTaskMonitoringFilter.taskId}
            </if>
            <if test="schTaskMonitoringFilter.computeDeviceId != null">
                AND sch_task_monitoring.compute_device_id = #{schTaskMonitoringFilter.computeDeviceId}
            </if>
            <if test="schTaskMonitoringFilter.resourceId != null">
                AND sch_task_monitoring.resource_id = #{schTaskMonitoringFilter.resourceId}
            </if>
            <if test="schTaskMonitoringFilter.cpuUsage != null">
                AND sch_task_monitoring.cpu_usage = #{schTaskMonitoringFilter.cpuUsage}
            </if>
            <if test="schTaskMonitoringFilter.containerId != null">
                AND sch_task_monitoring.container_id = #{schTaskMonitoringFilter.containerId}
            </if>
            <if test="schTaskMonitoringFilter.memoryUsage != null and schTaskMonitoringFilter.memoryUsage != ''">
                <bind name = "safeSchTaskMonitoringMemoryUsage" value = "'%' + schTaskMonitoringFilter.memoryUsage + '%'" />
                AND sch_task_monitoring.memory_usage LIKE #{safeSchTaskMonitoringMemoryUsage}
            </if>
            <if test="schTaskMonitoringFilter.graphicsMemoryUsage != null and schTaskMonitoringFilter.graphicsMemoryUsage != ''">
                <bind name = "safeSchTaskMonitoringGraphicsMemoryUsage" value = "'%' + schTaskMonitoringFilter.graphicsMemoryUsage + '%'" />
                AND sch_task_monitoring.graphics_memory_usage LIKE #{safeSchTaskMonitoringGraphicsMemoryUsage}
            </if>
            <if test="schTaskMonitoringFilter.tsStart != null and schTaskMonitoringFilter.tsStart != ''">
                AND sch_task_monitoring.ts &gt;= #{schTaskMonitoringFilter.tsStart}
            </if>
            <if test="schTaskMonitoringFilter.tsEnd != null and schTaskMonitoringFilter.tsEnd != ''">
                AND sch_task_monitoring.ts &lt;= #{schTaskMonitoringFilter.tsEnd}
            </if>
            <if test="schTaskMonitoringFilter.searchString != null and schTaskMonitoringFilter.searchString != ''">
                <bind name = "safeSchTaskMonitoringSearchString" value = "'%' + schTaskMonitoringFilter.searchString + '%'" />
                AND CONCAT(IFNULL(sch_task_monitoring.str_id,''), IFNULL(sch_task_monitoring.memory_usage,''), IFNULL(sch_task_monitoring.graphics_memory_usage,'')) LIKE #{safeSchTaskMonitoringSearchString}
            </if>
        </if>
    </sql>

    <select id="getGroupedSchTaskMonitoringList" resultMap="BaseResultMap" parameterType="supie.webadmin.app.model.SchTaskMonitoring">
        SELECT * FROM
            (SELECT
                COUNT(id) id,
                ${groupSelect}
            FROM sch_task_monitoring
            <where>
                <include refid="filterRef"/>
            </where>
            GROUP BY ${groupBy}) sch_task_monitoring
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>

    <select id="getSchTaskMonitoringList" resultMap="BaseResultMap" parameterType="supie.webadmin.app.model.SchTaskMonitoring">
        SELECT * FROM sch_task_monitoring
        <where>
            <include refid="filterRef"/>
        </where>
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>
    <select id="taskCount" resultType="supie.webadmin.upms.vo.TaskCount">
        SELECT
            COUNT(id)                                                                                    AS totalTask,
            SUM(IF(ti.approve_state = 'pending', 1, 0))                                                  AS noCommit,
            SUM(IF(ti.status= 'pending',1,0))															 AS pending,
            SUM(IF(ti.status = 'failed',1,0))                                                            AS failed,
            SUM(IF(ti.status = 'starting', 1, 0))                                                        AS start,
            SUM(IF(ti.status = 'queued', 1, 0))                                                          AS queue,
            SUM(IF(ti.status = 'running', 1, 0))                                                         AS run,
            SUM(IF(ti.status = 'finished', 1, 0))                                                        AS finish,
            SUM(IF(ti.status = 'stop' AND ti.container_status IN ('exited', 'paused','exited(0)'), 1, 0))            AS stop,
            SUM(IF(ti.status = 'running' AND ti.container_status IN ('dead','exited','paused','failed','restarting'), 1, 0)) AS exception
        FROM
            sch_task_info ti
        WHERE
            ti.is_delete = 1
    </select>
</mapper>
