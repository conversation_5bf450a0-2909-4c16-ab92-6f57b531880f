<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="supie.webadmin.app.dao.SchServiceManagerMapper">
    <resultMap id="BaseResultMap" type="supie.webadmin.app.model.SchServiceManager">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="service_name" jdbcType="VARCHAR" property="serviceName"/>
        <result column="namespace_id" jdbcType="BIGINT" property="namespaceId"/>
        <result column="cluster_id" jdbcType="BIGINT" property="clusterId"/>
        <result column="service_type" jdbcType="VARCHAR" property="serviceType"/>
        <result column="selector_labels" jdbcType="VARCHAR" property="selectorLabels"/>
        <result column="cluster_ip" jdbcType="VARCHAR" property="clusterIp"/>
        <result column="external_ips" jdbcType="VARCHAR" property="externalIps"/>
        <result column="ports" jdbcType="LONGVARCHAR" property="ports"/>
        <result column="load_balancer_ip" jdbcType="VARCHAR" property="loadBalancerIp"/>
        <result column="load_balancer_source_ranges" jdbcType="VARCHAR" property="loadBalancerSourceRanges"/>
        <result column="str_id" jdbcType="VARCHAR" property="strId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="create_user_id" jdbcType="BIGINT" property="createUserId"/>
        <result column="update_user_id" jdbcType="BIGINT" property="updateUserId"/>
        <result column="data_user_id" jdbcType="BIGINT" property="dataUserId"/>
        <result column="data_dept_id" jdbcType="BIGINT" property="dataDeptId"/>
        <result column="is_delete" jdbcType="INTEGER" property="isDelete"/>
    </resultMap>

    <insert id="insertList">
        INSERT INTO sch_service_manager
            (id,
            service_name,
            namespace_id,
            cluster_id,
            service_type,
            selector_labels,
            cluster_ip,
            external_ips,
            ports,
            load_balancer_ip,
            load_balancer_source_ranges,
            str_id,
            update_time,
            create_time,
            create_user_id,
            update_user_id,
            data_user_id,
            data_dept_id,
            is_delete)
        VALUES
        <foreach collection="list" index="index" item="item" separator="," >
            (#{item.id},
            #{item.serviceName},
            #{item.namespaceId},
            #{item.clusterId},
            #{item.serviceType},
            #{item.selectorLabels},
            #{item.clusterIp},
            #{item.externalIps},
            #{item.ports},
            #{item.loadBalancerIp},
            #{item.loadBalancerSourceRanges},
            #{item.strId},
            #{item.updateTime},
            #{item.createTime},
            #{item.createUserId},
            #{item.updateUserId},
            #{item.dataUserId},
            #{item.dataDeptId},
            #{item.isDelete})
        </foreach>
    </insert>

    <!-- 如果有逻辑删除字段过滤，请写到这里 -->
    <sql id="filterRef">
        <!-- 这里必须加上全包名，否则当filterRef被其他Mapper.xml包含引用的时候，就会调用Mapper.xml中的该SQL片段 -->
        <include refid="supie.webadmin.app.dao.SchServiceManagerMapper.inputFilterRef"/>
        AND sch_service_manager.is_delete = ${@supie.common.core.constant.GlobalDeletedFlag@NORMAL}
    </sql>

    <!-- 这里仅包含调用接口输入的主表过滤条件 -->
    <sql id="inputFilterRef">
        <if test="schServiceManagerFilter != null">
            <if test="schServiceManagerFilter.id != null">
                AND sch_service_manager.id = #{schServiceManagerFilter.id}
            </if>
            <if test="schServiceManagerFilter.serviceName != null and schServiceManagerFilter.serviceName != ''">
                <bind name = "safeSchServiceManagerServiceName" value = "'%' + schServiceManagerFilter.serviceName + '%'" />
                AND sch_service_manager.service_name LIKE #{safeSchServiceManagerServiceName}
            </if>
            <if test="schServiceManagerFilter.namespaceId != null">
                AND sch_service_manager.namespace_id = #{schServiceManagerFilter.namespaceId}
            </if>
            <if test="schServiceManagerFilter.clusterId != null">
                AND sch_service_manager.cluster_id = #{schServiceManagerFilter.clusterId}
            </if>
            <if test="schServiceManagerFilter.serviceType != null and schServiceManagerFilter.serviceType != ''">
                <bind name = "safeSchServiceManagerServiceType" value = "'%' + schServiceManagerFilter.serviceType + '%'" />
                AND sch_service_manager.service_type LIKE #{safeSchServiceManagerServiceType}
            </if>
            <if test="schServiceManagerFilter.selectorLabels != null and schServiceManagerFilter.selectorLabels != ''">
                <bind name = "safeSchServiceManagerSelectorLabels" value = "'%' + schServiceManagerFilter.selectorLabels + '%'" />
                AND sch_service_manager.selector_labels LIKE #{safeSchServiceManagerSelectorLabels}
            </if>
            <if test="schServiceManagerFilter.clusterIp != null and schServiceManagerFilter.clusterIp != ''">
                <bind name = "safeSchServiceManagerClusterIp" value = "'%' + schServiceManagerFilter.clusterIp + '%'" />
                AND sch_service_manager.cluster_ip LIKE #{safeSchServiceManagerClusterIp}
            </if>
            <if test="schServiceManagerFilter.externalIps != null and schServiceManagerFilter.externalIps != ''">
                <bind name = "safeSchServiceManagerExternalIps" value = "'%' + schServiceManagerFilter.externalIps + '%'" />
                AND sch_service_manager.external_ips LIKE #{safeSchServiceManagerExternalIps}
            </if>
            <if test="schServiceManagerFilter.ports != null and schServiceManagerFilter.ports != ''">
                <bind name = "safeSchServiceManagerPorts" value = "'%' + schServiceManagerFilter.ports + '%'" />
                AND sch_service_manager.ports LIKE #{safeSchServiceManagerPorts}
            </if>
            <if test="schServiceManagerFilter.loadBalancerIp != null and schServiceManagerFilter.loadBalancerIp != ''">
                <bind name = "safeSchServiceManagerLoadBalancerIp" value = "'%' + schServiceManagerFilter.loadBalancerIp + '%'" />
                AND sch_service_manager.load_balancer_ip LIKE #{safeSchServiceManagerLoadBalancerIp}
            </if>
            <if test="schServiceManagerFilter.loadBalancerSourceRanges != null and schServiceManagerFilter.loadBalancerSourceRanges != ''">
                <bind name = "safeSchServiceManagerLoadBalancerSourceRanges" value = "'%' + schServiceManagerFilter.loadBalancerSourceRanges + '%'" />
                AND sch_service_manager.load_balancer_source_ranges LIKE #{safeSchServiceManagerLoadBalancerSourceRanges}
            </if>
            <if test="schServiceManagerFilter.strId != null and schServiceManagerFilter.strId != ''">
                <bind name = "safeSchServiceManagerStrId" value = "'%' + schServiceManagerFilter.strId + '%'" />
                AND sch_service_manager.str_id LIKE #{safeSchServiceManagerStrId}
            </if>
            <if test="schServiceManagerFilter.updateTimeStart != null and schServiceManagerFilter.updateTimeStart != ''">
                AND sch_service_manager.update_time &gt;= #{schServiceManagerFilter.updateTimeStart}
            </if>
            <if test="schServiceManagerFilter.updateTimeEnd != null and schServiceManagerFilter.updateTimeEnd != ''">
                AND sch_service_manager.update_time &lt;= #{schServiceManagerFilter.updateTimeEnd}
            </if>
            <if test="schServiceManagerFilter.createTimeStart != null and schServiceManagerFilter.createTimeStart != ''">
                AND sch_service_manager.create_time &gt;= #{schServiceManagerFilter.createTimeStart}
            </if>
            <if test="schServiceManagerFilter.createTimeEnd != null and schServiceManagerFilter.createTimeEnd != ''">
                AND sch_service_manager.create_time &lt;= #{schServiceManagerFilter.createTimeEnd}
            </if>
            <if test="schServiceManagerFilter.createUserId != null">
                AND sch_service_manager.create_user_id = #{schServiceManagerFilter.createUserId}
            </if>
            <if test="schServiceManagerFilter.updateUserId != null">
                AND sch_service_manager.update_user_id = #{schServiceManagerFilter.updateUserId}
            </if>
            <if test="schServiceManagerFilter.dataUserId != null">
                AND sch_service_manager.data_user_id = #{schServiceManagerFilter.dataUserId}
            </if>
            <if test="schServiceManagerFilter.dataDeptId != null">
                AND sch_service_manager.data_dept_id = #{schServiceManagerFilter.dataDeptId}
            </if>
            <if test="schServiceManagerFilter.searchString != null and schServiceManagerFilter.searchString != ''">
                <bind name = "safeSchServiceManagerSearchString" value = "'%' + schServiceManagerFilter.searchString + '%'" />
                AND CONCAT(IFNULL(sch_service_manager.service_name,''), IFNULL(sch_service_manager.service_type,''), IFNULL(sch_service_manager.selector_labels,''), IFNULL(sch_service_manager.cluster_ip,''), IFNULL(sch_service_manager.external_ips,''), IFNULL(sch_service_manager.ports,''), IFNULL(sch_service_manager.load_balancer_ip,'')) LIKE #{safeSchServiceManagerSearchString}
            </if>
        </if>
    </sql>

    <select id="getGroupedSchServiceManagerList" resultMap="BaseResultMap" parameterType="supie.webadmin.app.model.SchServiceManager">
        SELECT * FROM
            (SELECT
                COUNT(id) id,
                ${groupSelect}
            FROM sch_service_manager
            <where>
                <include refid="filterRef"/>
            </where>
            GROUP BY ${groupBy}) sch_service_manager
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>

    <select id="getSchServiceManagerList" resultMap="BaseResultMap" parameterType="supie.webadmin.app.model.SchServiceManager">
        SELECT * FROM sch_service_manager
        <where>
            <include refid="filterRef"/>
        </where>
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>
</mapper>
