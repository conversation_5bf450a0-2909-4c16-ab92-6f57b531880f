<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="supie.webadmin.app.dao.SchResourceSliceMapper">
    <resultMap id="BaseResultMap" type="supie.webadmin.app.model.SchResourceSlice">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="str_id" jdbcType="VARCHAR" property="strId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="create_user_id" jdbcType="BIGINT" property="createUserId"/>
        <result column="update_user_id" jdbcType="BIGINT" property="updateUserId"/>
        <result column="data_user_id" jdbcType="BIGINT" property="dataUserId"/>
        <result column="data_dept_id" jdbcType="BIGINT" property="dataDeptId"/>
        <result column="is_delete" jdbcType="INTEGER" property="isDelete"/>
        <result column="partition_name" jdbcType="VARCHAR" property="partitionName"/>
        <result column="resource_id" jdbcType="BIGINT" property="resourceId"/>
        <result column="compute_device_id" jdbcType="BIGINT" property="computeDeviceId"/>
        <result column="compute_percent" jdbcType="VARCHAR" property="computePercent"/>
        <result column="compute_device_index" jdbcType="INTEGER" property="computeDeviceIndex"/>
        <result column="graphics_memory_size" jdbcType="INTEGER" property="graphicsMemorySize"/>
        <result column="graphics_memory_rate" jdbcType="DECIMAL" property="graphicsMemoryRate"/>
        <result column="memory_size" jdbcType="INTEGER" property="memorySize"/>
        <result column="isolation_mode" jdbcType="VARCHAR" property="isolationMode"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="task_id" jdbcType="BIGINT" property="taskId"/>
    </resultMap>

    <insert id="insertList">
        INSERT INTO sch_resource_slice
            (id,
            str_id,
            update_time,
            create_time,
            create_user_id,
            update_user_id,
            data_user_id,
            data_dept_id,
            is_delete,
            partition_name,
            resource_id,
            compute_device_id,
            compute_percent,
            compute_device_index,
            graphics_memory_size,
            graphics_memory_rate,
            memory_size,
            isolation_mode,
            status,
            task_id)
        VALUES
        <foreach collection="list" index="index" item="item" separator="," >
            (#{item.id},
            #{item.strId},
            #{item.updateTime},
            #{item.createTime},
            #{item.createUserId},
            #{item.updateUserId},
            #{item.dataUserId},
            #{item.dataDeptId},
            #{item.isDelete},
            #{item.partitionName},
            #{item.resourceId},
            #{item.computeDeviceId},
            #{item.computePercent},
            #{item.computeDeviceIndex},
            #{item.graphicsMemorySize},
            #{item.graphicsMemoryRate},
            #{item.memorySize},
            #{item.isolationMode},
            #{item.status},
            #{item.taskId})
        </foreach>
    </insert>

    <!-- 如果有逻辑删除字段过滤，请写到这里 -->
    <sql id="filterRef">
        <!-- 这里必须加上全包名，否则当filterRef被其他Mapper.xml包含引用的时候，就会调用Mapper.xml中的该SQL片段 -->
        <include refid="supie.webadmin.app.dao.SchResourceSliceMapper.inputFilterRef"/>
        AND sch_resource_slice.is_delete = ${@supie.common.core.constant.GlobalDeletedFlag@NORMAL}
    </sql>

    <!-- 这里仅包含调用接口输入的主表过滤条件 -->
    <sql id="inputFilterRef">
        <if test="schResourceSliceFilter != null">
            <if test="schResourceSliceFilter.id != null">
                AND sch_resource_slice.id = #{schResourceSliceFilter.id}
            </if>
            <if test="schResourceSliceFilter.strId != null and schResourceSliceFilter.strId != ''">
                <bind name = "safeSchResourceSliceStrId" value = "'%' + schResourceSliceFilter.strId + '%'" />
                AND sch_resource_slice.str_id LIKE #{safeSchResourceSliceStrId}
            </if>
            <if test="schResourceSliceFilter.updateTimeStart != null and schResourceSliceFilter.updateTimeStart != ''">
                AND sch_resource_slice.update_time &gt;= #{schResourceSliceFilter.updateTimeStart}
            </if>
            <if test="schResourceSliceFilter.updateTimeEnd != null and schResourceSliceFilter.updateTimeEnd != ''">
                AND sch_resource_slice.update_time &lt;= #{schResourceSliceFilter.updateTimeEnd}
            </if>
            <if test="schResourceSliceFilter.createTimeStart != null and schResourceSliceFilter.createTimeStart != ''">
                AND sch_resource_slice.create_time &gt;= #{schResourceSliceFilter.createTimeStart}
            </if>
            <if test="schResourceSliceFilter.createTimeEnd != null and schResourceSliceFilter.createTimeEnd != ''">
                AND sch_resource_slice.create_time &lt;= #{schResourceSliceFilter.createTimeEnd}
            </if>
            <if test="schResourceSliceFilter.createUserId != null">
                AND sch_resource_slice.create_user_id = #{schResourceSliceFilter.createUserId}
            </if>
            <if test="schResourceSliceFilter.updateUserId != null">
                AND sch_resource_slice.update_user_id = #{schResourceSliceFilter.updateUserId}
            </if>
            <if test="schResourceSliceFilter.dataUserId != null">
                AND sch_resource_slice.data_user_id = #{schResourceSliceFilter.dataUserId}
            </if>
            <if test="schResourceSliceFilter.dataDeptId != null">
                AND sch_resource_slice.data_dept_id = #{schResourceSliceFilter.dataDeptId}
            </if>
            <if test="schResourceSliceFilter.isDelete != null">
                AND sch_resource_slice.is_delete = #{schResourceSliceFilter.isDelete}
            </if>
            <if test="schResourceSliceFilter.partitionName != null and schResourceSliceFilter.partitionName != ''">
                <bind name = "safeSchResourceSlicePartitionName" value = "'%' + schResourceSliceFilter.partitionName + '%'" />
                AND sch_resource_slice.partition_name LIKE #{safeSchResourceSlicePartitionName}
            </if>
            <if test="schResourceSliceFilter.resourceId != null">
                AND sch_resource_slice.resource_id = #{schResourceSliceFilter.resourceId}
            </if>
            <if test="schResourceSliceFilter.computeDeviceId != null">
                AND sch_resource_slice.compute_device_id = #{schResourceSliceFilter.computeDeviceId}
            </if>
            <if test="schResourceSliceFilter.computePercent != null and schResourceSliceFilter.computePercent != ''">
                <bind name = "safeSchResourceSliceComputePercent" value = "'%' + schResourceSliceFilter.computePercent + '%'" />
                AND sch_resource_slice.compute_percent LIKE #{safeSchResourceSliceComputePercent}
            </if>
            <if test="schResourceSliceFilter.computeDeviceIndex != null">
                AND sch_resource_slice.compute_device_index = #{schResourceSliceFilter.computeDeviceIndex}
            </if>
            <if test="schResourceSliceFilter.graphicsMemorySize != null">
                AND sch_resource_slice.graphics_memory_size = #{schResourceSliceFilter.graphicsMemorySize}
            </if>
            <if test="schResourceSliceFilter.graphicsMemoryRate != null">
                AND sch_resource_slice.graphics_memory_rate = #{schResourceSliceFilter.graphicsMemoryRate}
            </if>
            <if test="schResourceSliceFilter.memorySize != null">
                AND sch_resource_slice.memory_size = #{schResourceSliceFilter.memorySize}
            </if>
            <if test="schResourceSliceFilter.isolationMode != null and schResourceSliceFilter.isolationMode != ''">
                AND sch_resource_slice.isolation_mode = #{schResourceSliceFilter.isolationMode}
            </if>
            <if test="schResourceSliceFilter.status != null and schResourceSliceFilter.status != ''">
                AND sch_resource_slice.status = #{schResourceSliceFilter.status}
            </if>
            <if test="schResourceSliceFilter.taskId != null">
                AND sch_resource_slice.task_id = #{schResourceSliceFilter.taskId}
            </if>
            <if test="schResourceSliceFilter.searchString != null and schResourceSliceFilter.searchString != ''">
                <bind name = "safeSchResourceSliceSearchString" value = "'%' + schResourceSliceFilter.searchString + '%'" />
                AND CONCAT(IFNULL(sch_resource_slice.str_id,''), IFNULL(sch_resource_slice.partition_name,''), IFNULL(sch_resource_slice.compute_percent,''), IFNULL(sch_resource_slice.isolation_mode,''), IFNULL(sch_resource_slice.status,'')) LIKE #{safeSchResourceSliceSearchString}
            </if>
        </if>
    </sql>

    <select id="getGroupedSchResourceSliceList" resultMap="BaseResultMap" parameterType="supie.webadmin.app.model.SchResourceSlice">
        SELECT * FROM
            (SELECT
                COUNT(id) id,
                ${groupSelect}
            FROM sch_resource_slice
            <where>
                <include refid="filterRef"/>
            </where>
            GROUP BY ${groupBy}) sch_resource_slice
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>

    <select id="getSchResourceSliceList" resultMap="BaseResultMap" parameterType="supie.webadmin.app.model.SchResourceSlice">
        SELECT * FROM sch_resource_slice
        <where>
            <include refid="filterRef"/>
        </where>
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>
</mapper>
