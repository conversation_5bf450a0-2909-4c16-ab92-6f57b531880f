<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="supie.webadmin.app.dao.SchScalePlanMapper">
    <resultMap id="BaseResultMap" type="supie.webadmin.app.model.SchScalePlan">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="expand_core_num" jdbcType="INTEGER" property="expandCoreNum"/>
        <result column="expand_memory_num" jdbcType="BIGINT" property="expandMemoryNum"/>
        <result column="cpu_threshold_ex" jdbcType="INTEGER" property="cpuThresholdEx"/>
        <result column="memory_threshold_ex" jdbcType="INTEGER" property="memoryThresholdEx"/>
        <result column="graphic_threshold_ex" jdbcType="INTEGER" property="graphicThresholdEx"/>
        <result column="cpu_threshold_dc" jdbcType="INTEGER" property="cpuThresholdDc"/>
        <result column="memory_threshold_dc" jdbcType="INTEGER" property="memoryThresholdDc"/>
        <result column="graphic_threshold_dc" jdbcType="INTEGER" property="graphicThresholdDc"/>
        <result column="operation_type" jdbcType="VARCHAR" property="operationType"/>
        <result column="resource_config" jdbcType="VARCHAR" property="resourceConfig"/>
        <result column="str_id" jdbcType="VARCHAR" property="strId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="create_user_id" jdbcType="BIGINT" property="createUserId"/>
        <result column="update_user_id" jdbcType="BIGINT" property="updateUserId"/>
        <result column="data_user_id" jdbcType="BIGINT" property="dataUserId"/>
        <result column="data_dept_id" jdbcType="BIGINT" property="dataDeptId"/>
        <result column="is_delete" jdbcType="INTEGER" property="isDelete"/>
    </resultMap>

    <insert id="insertList">
        INSERT INTO sch_scale_plan
            (id,
            expand_core_num,
            expand_memory_num,
            cpu_threshold_ex,
            memory_threshold_ex,
            graphic_threshold_ex,
            cpu_threshold_dc,
            memory_threshold_dc,
            graphic_threshold_dc,
            operation_type,
            resource_config,
            str_id,
            update_time,
            create_time,
            create_user_id,
            update_user_id,
            data_user_id,
            data_dept_id,
            is_delete)
        VALUES
        <foreach collection="list" index="index" item="item" separator="," >
            (#{item.id},
            #{item.expandCoreNum},
            #{item.expandMemoryNum},
            #{item.cpuThresholdEx},
            #{item.memoryThresholdEx},
            #{item.graphicThresholdEx},
            #{item.cpuThresholdDc},
            #{item.memoryThresholdDc},
            #{item.graphicThresholdDc},
            #{item.operationType},
            #{item.resourceConfig},
            #{item.strId},
            #{item.updateTime},
            #{item.createTime},
            #{item.createUserId},
            #{item.updateUserId},
            #{item.dataUserId},
            #{item.dataDeptId},
            #{item.isDelete})
        </foreach>
    </insert>

    <!-- 如果有逻辑删除字段过滤，请写到这里 -->
    <sql id="filterRef">
        <!-- 这里必须加上全包名，否则当filterRef被其他Mapper.xml包含引用的时候，就会调用Mapper.xml中的该SQL片段 -->
        <include refid="supie.webadmin.app.dao.SchScalePlanMapper.inputFilterRef"/>
        AND sch_scale_plan.is_delete = ${@supie.common.core.constant.GlobalDeletedFlag@NORMAL}
    </sql>

    <!-- 这里仅包含调用接口输入的主表过滤条件 -->
    <sql id="inputFilterRef">
        <if test="schScalePlanFilter != null">
            <if test="schScalePlanFilter.id != null">
                AND sch_scale_plan.id = #{schScalePlanFilter.id}
            </if>
            <if test="schScalePlanFilter.expandCoreNum != null">
                AND sch_scale_plan.expand_core_num = #{schScalePlanFilter.expandCoreNum}
            </if>
            <if test="schScalePlanFilter.expandMemoryNum != null">
                AND sch_scale_plan.expand_memory_num = #{schScalePlanFilter.expandMemoryNum}
            </if>
            <if test="schScalePlanFilter.cpuThresholdEx != null">
                AND sch_scale_plan.cpu_threshold_ex = #{schScalePlanFilter.cpuThresholdEx}
            </if>
            <if test="schScalePlanFilter.memoryThresholdEx != null">
                AND sch_scale_plan.memory_threshold_ex = #{schScalePlanFilter.memoryThresholdEx}
            </if>
            <if test="schScalePlanFilter.graphicThresholdEx != null">
                AND sch_scale_plan.graphic_threshold_ex = #{schScalePlanFilter.graphicThresholdEx}
            </if>
            <if test="schScalePlanFilter.cpuThresholdDc != null">
                AND sch_scale_plan.cpu_threshold_dc = #{schScalePlanFilter.cpuThresholdDc}
            </if>
            <if test="schScalePlanFilter.memoryThresholdDc != null">
                AND sch_scale_plan.memory_threshold_dc = #{schScalePlanFilter.memoryThresholdDc}
            </if>
            <if test="schScalePlanFilter.graphicThresholdDc != null">
                AND sch_scale_plan.graphic_threshold_dc = #{schScalePlanFilter.graphicThresholdDc}
            </if>
            <if test="schScalePlanFilter.operationType != null and schScalePlanFilter.operationType != ''">
                <bind name = "safeSchScalePlanOperationType" value = "'%' + schScalePlanFilter.operationType + '%'" />
                AND sch_scale_plan.operation_type LIKE #{safeSchScalePlanOperationType}
            </if>
            <if test="schScalePlanFilter.resourceConfig != null and schScalePlanFilter.resourceConfig != ''">
                <bind name = "safeSchScalePlanResourceConfig" value = "'%' + schScalePlanFilter.resourceConfig + '%'" />
                AND sch_scale_plan.resource_config LIKE #{safeSchScalePlanResourceConfig}
            </if>
            <if test="schScalePlanFilter.strId != null and schScalePlanFilter.strId != ''">
                <bind name = "safeSchScalePlanStrId" value = "'%' + schScalePlanFilter.strId + '%'" />
                AND sch_scale_plan.str_id LIKE #{safeSchScalePlanStrId}
            </if>
            <if test="schScalePlanFilter.updateTimeStart != null and schScalePlanFilter.updateTimeStart != ''">
                AND sch_scale_plan.update_time &gt;= #{schScalePlanFilter.updateTimeStart}
            </if>
            <if test="schScalePlanFilter.updateTimeEnd != null and schScalePlanFilter.updateTimeEnd != ''">
                AND sch_scale_plan.update_time &lt;= #{schScalePlanFilter.updateTimeEnd}
            </if>
            <if test="schScalePlanFilter.createTimeStart != null and schScalePlanFilter.createTimeStart != ''">
                AND sch_scale_plan.create_time &gt;= #{schScalePlanFilter.createTimeStart}
            </if>
            <if test="schScalePlanFilter.createTimeEnd != null and schScalePlanFilter.createTimeEnd != ''">
                AND sch_scale_plan.create_time &lt;= #{schScalePlanFilter.createTimeEnd}
            </if>
            <if test="schScalePlanFilter.createUserId != null">
                AND sch_scale_plan.create_user_id = #{schScalePlanFilter.createUserId}
            </if>
            <if test="schScalePlanFilter.updateUserId != null">
                AND sch_scale_plan.update_user_id = #{schScalePlanFilter.updateUserId}
            </if>
            <if test="schScalePlanFilter.dataUserId != null">
                AND sch_scale_plan.data_user_id = #{schScalePlanFilter.dataUserId}
            </if>
            <if test="schScalePlanFilter.dataDeptId != null">
                AND sch_scale_plan.data_dept_id = #{schScalePlanFilter.dataDeptId}
            </if>
            <if test="schScalePlanFilter.isDelete != null">
                AND sch_scale_plan.is_delete = #{schScalePlanFilter.isDelete}
            </if>
            <if test="schScalePlanFilter.searchString != null and schScalePlanFilter.searchString != ''">
                <bind name = "safeSchScalePlanSearchString" value = "'%' + schScalePlanFilter.searchString + '%'" />
                AND CONCAT(IFNULL(sch_scale_plan.operation_type,''), IFNULL(sch_scale_plan.resource_config,'')) LIKE #{safeSchScalePlanSearchString}
            </if>
        </if>
    </sql>

    <select id="getGroupedSchScalePlanList" resultMap="BaseResultMap" parameterType="supie.webadmin.app.model.SchScalePlan">
        SELECT * FROM
            (SELECT
                SUM(expand_core_num) expand_core_num,
                ${groupSelect}
            FROM sch_scale_plan
            <where>
                <include refid="filterRef"/>
            </where>
            GROUP BY ${groupBy}) sch_scale_plan
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>

    <select id="getSchScalePlanList" resultMap="BaseResultMap" parameterType="supie.webadmin.app.model.SchScalePlan">
        SELECT * FROM sch_scale_plan
        <where>
            <include refid="filterRef"/>
        </where>
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>
</mapper>
