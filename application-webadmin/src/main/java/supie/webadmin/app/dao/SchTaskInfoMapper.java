package supie.webadmin.app.dao;

import supie.common.core.base.dao.BaseDaoMapper;
import supie.webadmin.app.model.SchTaskInfo;
import org.apache.ibatis.annotations.Param;
import supie.webadmin.app.vo.SchTaskInfoVo;

import java.util.*;
import java.util.List;
import java.util.Map;
/**
 * 任务表数据操作访问接口。
 *
 * <AUTHOR> -rf .bug
 * @date 2025-05-16
 */
public interface SchTaskInfoMapper extends BaseDaoMapper<SchTaskInfo> {

    /**
     * 批量插入对象列表。
     *
     * @param schTaskInfoList 新增对象列表。
     */
    void insertList(List<SchTaskInfo> schTaskInfoList);

    /**
     * 获取分组计算后的数据对象列表。
     *
     * @param schTaskInfoFilter 主表过滤对象。
     * @param groupSelect 分组显示字段列表字符串，SELECT从句的参数。
     * @param groupBy 分组字段列表字符串，GROUP BY从句的参数。
     * @param orderBy 排序字符串，ORDER BY从句的参数。
     * @return 对象列表。
     */
    List<SchTaskInfo> getGroupedSchTaskInfoList(
            @Param("schTaskInfoFilter") SchTaskInfo schTaskInfoFilter,
            @Param("groupSelect") String groupSelect,
            @Param("groupBy") String groupBy,
            @Param("orderBy") String orderBy);

    /**
     * 获取过滤后的对象列表。
     *
     * @param schTaskInfoFilter 主表过滤对象。
     * @param orderBy 排序字符串，order by从句的参数。
     * @return 对象列表。
     */
    List<SchTaskInfo> getSchTaskInfoList(
            @Param("schTaskInfoFilter") SchTaskInfo schTaskInfoFilter, @Param("orderBy") String orderBy);

    /**
     * 查询启动任务是否审核通过
     *
     * @param taskId  当前任务id
     * @return 查询数据结果
     */
    SchTaskInfo queryApproval(@Param("id") Long taskId);

    /*
    *根据容器名查询容器数量
     */
    Integer countByContainerName();
    /*
     *查询在运行节点数量
     */
    Integer getNodeNumber();
    /*
     *查询不同状态的任务数量
     */
    List<Map<String, Object>> getTaskStatusCount();

    /**
     *
     * @param taskId 任务id
     * @return 当前任务节点服务器切分资源Id 列表
     */
    List<Integer> queryInfoVnpu(@Param("id") Long taskId);


    /**
     * 查询可运行任务vnpui
     * @param runCom 运行的卡ID
     * @param runPartition 运行的分区ID
     * @return 可用的空闲VNPU
     */
    List<Integer> queryAvaliable(@Param("runCom") List<Long> runCom, @Param("runP") List<Long> runPartition);

    /**
     * 根据任务ID 查询任务状态
     * @param taskIdList 任务id列表
     * @return 任务状态列表
     */
    List<SchTaskInfo> remoteTaskStatus(@Param("id") List<String> taskIdList);

    /**
     * 任务信息结果
     * @param aLong  主键id
     * @return 信息结结果
     */
    SchTaskInfoVo queryStatus(@Param("id") Long aLong);

    /**
     * 查询当前正在执行的任务信息列表
     * @return 在运行任务信息列表
     */
    List<SchTaskInfo> queryRunTask();

    /**
     *  查询再用任务情况
     * @param id  任务id
     * @param computeDeviceId 计算卡id
     * @param vccId  vnpuId
     * @return 任务信息列表
     */
    List<SchTaskInfo> usageQuery(@Param("id") Long id, @Param("device") String computeDeviceId, @Param("vccId") String vccId);

    /**
     * 收集正在运行再用卡的 任务 'running', 'restarting', 'paused', 'created
     * @param computerId 满足算力需求的卡id
     * @return 正在运行任务列表
     */
    List<Long> collectRunTask(@Param("id") List<Long> computerId);

    /**
     * 抽取空闲资源 exited(0) exited(非零)
     * @return 空闲资源partitionId
     */
    List<Long> extractResource();

    /**
     * 获取检测任务列表
     * @return 待检测更新任务列表
     */
    List<SchTaskInfo> checkTaskList();


}
