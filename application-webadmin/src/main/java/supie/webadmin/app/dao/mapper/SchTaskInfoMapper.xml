<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="supie.webadmin.app.dao.SchTaskInfoMapper">
    <resultMap id="BaseResultMap" type="supie.webadmin.app.model.SchTaskInfo">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="str_id" jdbcType="VARCHAR" property="strId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="create_user_id" jdbcType="BIGINT" property="createUserId"/>
        <result column="update_user_id" jdbcType="BIGINT" property="updateUserId"/>
        <result column="data_user_id" jdbcType="BIGINT" property="dataUserId"/>
        <result column="data_dept_id" jdbcType="BIGINT" property="dataDeptId"/>
        <result column="is_delete" jdbcType="INTEGER" property="isDelete"/>
        <result column="task_name" jdbcType="VARCHAR" property="taskName"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="task_priority" jdbcType="INTEGER" property="taskPriority"/>
        <result column="graphic_needed_mb" jdbcType="INTEGER" property="graphicNeededMb"/>
        <result column="memory_needed_mb" jdbcType="INTEGER" property="memoryNeededMb"/>
        <result column="cpu_need" jdbcType="VARCHAR" property="cpuNeed"/>
        <result column="pool_id" jdbcType="BIGINT" property="poolId"/>
        <result column="resource_id" jdbcType="BIGINT" property="resourceId"/>
        <result column="task_image_id" jdbcType="BIGINT" property="taskImageId"/>
        <result column="compute_device_id" jdbcType="BIGINT" property="computeDeviceId"/>
        <result column="partition_id" jdbcType="BIGINT" property="partitionId"/>
        <result column="run_command" jdbcType="VARCHAR" property="runCommand"/>
        <result column="env_config" jdbcType="VARCHAR" property="envConfig"/>
        <result column="release_policy" jdbcType="INTEGER" property="releasePolicy"/>
        <result column="start_time" jdbcType="TIMESTAMP" property="startTime"/>
        <result column="end_tiem" jdbcType="TIMESTAMP" property="endTiem"/>
        <result column="estimat_time" jdbcType="TIMESTAMP" property="estimatTime"/>
        <result column="scheduling_policies" jdbcType="INTEGER" property="schedulingPolicies"/>
        <result column="approve_state" jdbcType="VARCHAR" property="approveState"/>
        <result column="allow_preemption" jdbcType="INTEGER" property="allowPreemption"/>
        <result column="container_name"  property="containerName"/>
        <result column="container_status"  property="containerStatus"/>
        <result column="scale_plan_id"  property="scalePlanId"/>
        <result column="fail_reason"  property="failReason"/>
    </resultMap>

    <insert id="insertList">
        INSERT INTO sch_task_info
            (id,
            str_id,
            update_time,
            create_time,
            create_user_id,
            update_user_id,
            data_user_id,
            data_dept_id,
            is_delete,
            task_name,
            status,
            task_priority,
            graphic_needed_mb,
            memory_needed_mb,
            cpu_need,
            pool_id,
            resource_id,
            task_image_id,
            compute_device_id,
            partition_id,
            run_command,
            env_config,
            release_policy,
            start_time,
            end_tiem,
            estimat_time,
            scheduling_policies,
            approve_state,
            allow_preemption,
            container_name,
            container_status,
            dict_id,
            scale_plan_id,
            fail_reason
             )
        VALUES
        <foreach collection="list" index="index" item="item" separator="," >
            (#{item.id},
            #{item.strId},
            #{item.updateTime},
            #{item.createTime},
            #{item.createUserId},
            #{item.updateUserId},
            #{item.dataUserId},
            #{item.dataDeptId},
            #{item.isDelete},
            #{item.taskName},
            #{item.status},
            #{item.taskPriority},
            #{item.graphicNeededMb},
            #{item.memoryNeededMb},
            #{item.cpuNeed},
            #{item.poolId},
            #{item.resourceId},
            #{item.taskImageId},
            #{item.computeDeviceId},
            #{item.partitionId},
            #{item.runCommand},
            #{item.envConfig},
            #{item.releasePolicy},
            #{item.startTime},
            #{item.endTiem},
            #{item.estimatTime},
            #{item.schedulingPolicies},
            #{item.approveState},
            #{item.allowPreemption},
            #{item.containerName},
            #{item.containerStatus},
             #{item.dictId},
            #{item.scalePlanId},
            #{item.failReason}
            )
        </foreach>
    </insert>

    <!-- 如果有逻辑删除字段过滤，请写到这里 -->
    <sql id="filterRef">
        <!-- 这里必须加上全包名，否则当filterRef被其他Mapper.xml包含引用的时候，就会调用Mapper.xml中的该SQL片段 -->
        <include refid="supie.webadmin.app.dao.SchTaskInfoMapper.inputFilterRef"/>
        AND sch_task_info.is_delete = ${@supie.common.core.constant.GlobalDeletedFlag@NORMAL}
    </sql>

    <!-- 这里仅包含调用接口输入的主表过滤条件 -->
    <sql id="inputFilterRef">
        <if test="schTaskInfoFilter != null">
            <if test="schTaskInfoFilter.id != null">
                AND sch_task_info.id = #{schTaskInfoFilter.id}
            </if>

            <if test="schTaskInfoFilter.failReason != null">
                AND sch_task_info.fail_reason = #{schTaskInfoFilter.failReason}
            </if>

            <if test="schTaskInfoFilter.dictId != null">
                AND sch_task_info.dict_id = #{schTaskInfoFilter.dictId}
            </if>

            <if test="schTaskInfoFilter.containerName != null">
                AND sch_task_info.container_name = #{schTaskInfoFilter.containerName}
            </if>

            <if test="schTaskInfoFilter.containerStatus != null">
                AND sch_task_info.container_status = #{schTaskInfoFilter.containerStatus}
            </if>
            <if test="schTaskInfoFilter.strId != null and schTaskInfoFilter.strId != ''">
                <bind name = "safeSchTaskInfoStrId" value = "'%' + schTaskInfoFilter.strId + '%'" />
                AND sch_task_info.str_id LIKE #{safeSchTaskInfoStrId}
            </if>
            <if test="schTaskInfoFilter.updateTimeStart != null and schTaskInfoFilter.updateTimeStart != ''">
                AND sch_task_info.update_time &gt;= #{schTaskInfoFilter.updateTimeStart}
            </if>
            <if test="schTaskInfoFilter.updateTimeEnd != null and schTaskInfoFilter.updateTimeEnd != ''">
                AND sch_task_info.update_time &lt;= #{schTaskInfoFilter.updateTimeEnd}
            </if>
            <if test="schTaskInfoFilter.createTimeStart != null and schTaskInfoFilter.createTimeStart != ''">
                AND sch_task_info.create_time &gt;= #{schTaskInfoFilter.createTimeStart}
            </if>
            <if test="schTaskInfoFilter.createTimeEnd != null and schTaskInfoFilter.createTimeEnd != ''">
                AND sch_task_info.create_time &lt;= #{schTaskInfoFilter.createTimeEnd}
            </if>
            <if test="schTaskInfoFilter.createUserId != null">
                AND sch_task_info.create_user_id = #{schTaskInfoFilter.createUserId}
            </if>
            <if test="schTaskInfoFilter.updateUserId != null">
                AND sch_task_info.update_user_id = #{schTaskInfoFilter.updateUserId}
            </if>
            <if test="schTaskInfoFilter.dataUserId != null">
                AND sch_task_info.data_user_id = #{schTaskInfoFilter.dataUserId}
            </if>
            <if test="schTaskInfoFilter.dataDeptId != null">
                AND sch_task_info.data_dept_id = #{schTaskInfoFilter.dataDeptId}
            </if>
            <if test="schTaskInfoFilter.isDelete != null">
                AND sch_task_info.is_delete = #{schTaskInfoFilter.isDelete}
            </if>
            <if test="schTaskInfoFilter.taskName != null and schTaskInfoFilter.taskName != ''">
                <bind name = "safeSchTaskInfoTaskName" value = "'%' + schTaskInfoFilter.taskName + '%'" />
                AND sch_task_info.task_name LIKE #{safeSchTaskInfoTaskName}
            </if>
            <if test="schTaskInfoFilter.status != null and schTaskInfoFilter.status != ''">
                <bind name = "safeSchTaskInfoStatus" value = "'%' + schTaskInfoFilter.status + '%'" />
                AND sch_task_info.status LIKE #{safeSchTaskInfoStatus}
            </if>
            <if test="schTaskInfoFilter.taskPriority != null">
                AND sch_task_info.task_priority = #{schTaskInfoFilter.taskPriority}
            </if>
            <if test="schTaskInfoFilter.graphicNeededMb != null">
                AND sch_task_info.graphic_needed_mb = #{schTaskInfoFilter.graphicNeededMb}
            </if>
            <if test="schTaskInfoFilter.memoryNeededMb != null">
                AND sch_task_info.memory_needed_mb = #{schTaskInfoFilter.memoryNeededMb}
            </if>
            <if test="schTaskInfoFilter.cpuNeed != null and schTaskInfoFilter.cpuNeed != ''">
                <bind name = "safeSchTaskInfoCpuNeed" value = "'%' + schTaskInfoFilter.cpuNeed + '%'" />
                AND sch_task_info.cpu_need LIKE #{safeSchTaskInfoCpuNeed}
            </if>
            <if test="schTaskInfoFilter.poolId != null">
                AND sch_task_info.pool_id = #{schTaskInfoFilter.poolId}
            </if>
            <if test="schTaskInfoFilter.resourceId != null">
                AND sch_task_info.resource_id = #{schTaskInfoFilter.resourceId}
            </if>
            <if test="schTaskInfoFilter.taskImageId != null">
                AND sch_task_info.task_image_id = #{schTaskInfoFilter.taskImageId}
            </if>
            <if test="schTaskInfoFilter.computeDeviceId != null">
                AND sch_task_info.compute_device_id = #{schTaskInfoFilter.computeDeviceId}
            </if>
            <if test="schTaskInfoFilter.partitionId != null">
                AND sch_task_info.partition_id = #{schTaskInfoFilter.partitionId}
            </if>
            <if test="schTaskInfoFilter.runCommand != null and schTaskInfoFilter.runCommand != ''">
                <bind name = "safeSchTaskInfoRunCommand" value = "'%' + schTaskInfoFilter.runCommand + '%'" />
                AND sch_task_info.run_command LIKE #{safeSchTaskInfoRunCommand}
            </if>
            <if test="schTaskInfoFilter.envConfig != null and schTaskInfoFilter.envConfig != ''">
                <bind name = "safeSchTaskInfoEnvConfig" value = "'%' + schTaskInfoFilter.envConfig + '%'" />
                AND sch_task_info.env_config LIKE #{safeSchTaskInfoEnvConfig}
            </if>
            <if test="schTaskInfoFilter.releasePolicy != null">
                AND sch_task_info.release_policy = #{schTaskInfoFilter.releasePolicy}
            </if>
            <if test="schTaskInfoFilter.startTimeStart != null and schTaskInfoFilter.startTimeStart != ''">
                AND sch_task_info.start_time &gt;= #{schTaskInfoFilter.startTimeStart}
            </if>
            <if test="schTaskInfoFilter.startTimeEnd != null and schTaskInfoFilter.startTimeEnd != ''">
                AND sch_task_info.start_time &lt;= #{schTaskInfoFilter.startTimeEnd}
            </if>
            <if test="schTaskInfoFilter.endTiemStart != null and schTaskInfoFilter.endTiemStart != ''">
                AND sch_task_info.end_tiem &gt;= #{schTaskInfoFilter.endTiemStart}
            </if>
            <if test="schTaskInfoFilter.endTiemEnd != null and schTaskInfoFilter.endTiemEnd != ''">
                AND sch_task_info.end_tiem &lt;= #{schTaskInfoFilter.endTiemEnd}
            </if>
            <if test="schTaskInfoFilter.estimatTimeStart != null and schTaskInfoFilter.estimatTimeStart != ''">
                AND sch_task_info.estimat_time &gt;= #{schTaskInfoFilter.estimatTimeStart}
            </if>
            <if test="schTaskInfoFilter.estimatTimeEnd != null and schTaskInfoFilter.estimatTimeEnd != ''">
                AND sch_task_info.estimat_time &lt;= #{schTaskInfoFilter.estimatTimeEnd}
            </if>
            <if test="schTaskInfoFilter.schedulingPolicies != null">
                AND sch_task_info.scheduling_policies = #{schTaskInfoFilter.schedulingPolicies}
            </if>
            <if test="schTaskInfoFilter.approveState != null and schTaskInfoFilter.approveState != ''">
                <bind name = "safeSchTaskInfoApproveState" value = "'%' + schTaskInfoFilter.approveState + '%'" />
                AND sch_task_info.approve_state LIKE #{safeSchTaskInfoApproveState}
            </if>
            <if test="schTaskInfoFilter.allowPreemption != null">
                AND sch_task_info.allow_preemption = #{schTaskInfoFilter.allowPreemption}
            </if>
            <if test="schTaskInfoFilter.scalePlanId != null">
                AND sch_task_info.scale_plan_id = #{schTaskInfoFilter.scalePlanId}
            </if>
            <if test="schTaskInfoFilter.searchString != null and schTaskInfoFilter.searchString != ''">
                <bind name = "safeSchTaskInfoSearchString" value = "'%' + schTaskInfoFilter.searchString + '%'" />
                AND CONCAT(IFNULL(sch_task_info.str_id,''), IFNULL(sch_task_info.task_name,''), IFNULL(sch_task_info.status,''), IFNULL(sch_task_info.cpu_need,''), IFNULL(sch_task_info.run_command,''), IFNULL(sch_task_info.env_config,''), IFNULL(sch_task_info.approve_state,'')) LIKE #{safeSchTaskInfoSearchString}
            </if>
        </if>
    </sql>

    <select id="getGroupedSchTaskInfoList" resultMap="BaseResultMap" parameterType="supie.webadmin.app.model.SchTaskInfo">
        SELECT * FROM
            (SELECT
                COUNT(id) id,
                ${groupSelect}
            FROM sch_task_info
            <where>
                <include refid="filterRef"/>
            </where>
            GROUP BY ${groupBy}) sch_task_info
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>

    <select id="getSchTaskInfoList" resultMap="BaseResultMap" parameterType="supie.webadmin.app.model.SchTaskInfo">
        SELECT * FROM sch_task_info
        <where>
            <include refid="filterRef"/>
        </where>
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>
    <select id="queryApproval" resultType="supie.webadmin.app.model.SchTaskInfo">
        SELECT ti.*
        FROM sch_task_info ti
                 JOIN sch_task_approval ta ON ti.id = ta.task_id
        WHERE ta.task_id=#{id} AND ta.status ='approval'
    </select>
    <select id="countByContainerName" resultType="java.lang.Integer">
        SELECT COUNT(DISTINCT container_name) AS count
        FROM sch_task_info
        WHERE is_delete = 1
    </select>
    <select id="getNodeNumber" resultType="java.lang.Integer">
        SELECT COUNT(DISTINCT resource_id) AS count
        FROM sch_task_info
        WHERE is_delete = 1 and status = 'running'
    </select>
    <select id="getTaskStatusCount" resultType="map">
            SELECT s.status, COALESCE(t.count, 0) AS count
            FROM (
                SELECT 'running' AS status
                UNION ALL
                SELECT 'queued'
                UNION ALL
                SELECT 'starting'
                UNION ALL
                SELECT 'pending'
                UNION ALL
                SELECT 'stop'
                UNION ALL
                SELECT 'finished'
                UNION ALL
                SELECT 'failed'
                ) s
                LEFT JOIN (
                SELECT status, COUNT(*) AS count
                FROM sch_task_info
                WHERE is_delete = 1
                AND status IN ('pending', 'queued', 'starting', 'running', 'stop', 'finished', 'failed')
                GROUP BY status
                ) t ON s.status = t.status
            ORDER BY FIELD(s.status,
                'running',
                'queued',
                'starting',
                'pending',
                'stop',
                'finished',
                'failed'
                )
    </select>
    <select id="queryInfoVnpu" resultType="java.lang.Integer">
        SELECT vcc.vcc_id
        FROM sch_task_info ti
                 JOIN sch_compute_device cd ON cd.id = ti.compute_device_id
                 JOIN sch_virtual_compute_card_situation vcc ON vcc.compute_device_id = cd.id
        WHERE cd.is_delete = 1 AND ti.id=#{id}
          AND vcc.is_delete = 1
          AND ti.is_delete = 1
    </select>
    <select id="queryAvaliable" resultType="java.lang.Integer">
        SELECT vcc.vcc_id
        FROM sch_compute_device cd
        JOIN sch_virtual_compute_card_situation vcc ON cd.id = vcc.compute_device_id
        <where>
            cd.is_delete = 1
            AND vcc.is_delete = 1
            <if test="runCom != null">
                AND vcc.compute_device_id NOT IN
                <foreach item="item" collection="runCom" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
            <if test="runP != null">
                AND vcc.vcc_id NOT IN
                <foreach item="item" collection="runP" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
        </where>

    </select>
    <select id="remoteTaskStatus" resultType="supie.webadmin.app.model.SchTaskInfo">
        SELECT id, task_name, status, approve_state,fail_reason
        FROM sch_task_info
    <where>
        is_delete = 1
        AND id IN
        <foreach item="item" collection="id" separator="," open="(" close=")" index="">
            #{item}
        </foreach>
    </where>

    </select>
    <select id="queryStatus" resultType="supie.webadmin.app.vo.SchTaskInfoVo">
        SELECT id, approve_state, status, container_status
        FROM sch_task_info
        WHERE id = #{id}
    </select>
    <select id="queryRunTask" resultType="supie.webadmin.app.model.SchTaskInfo">
        SELECT ti.*
        FROM sch_task_info ti
                 LEFT JOIN sch_container_manager cm
                           ON ti.id = cm.task_info_id
        WHERE ti.is_delete = 1
          AND cm.is_delete = 1
          AND ti.status='running'
          AND cm.container_status IN ('running', 'exited', 'exited(0)')
    </select>
    <select id="usageQuery" resultType="supie.webadmin.app.model.SchTaskInfo">
        SELECT *
        FROM sch_task_info
        WHERE id != #{id}
          AND compute_device_id = #{device}
          AND partition_id = #{vccId}
          AND (
            status = 'running'
                OR (status = 'exited' AND container_status = 'running')
            )
    </select>
    <select id="collectRunTask" resultType="java.lang.Long">
        SELECT partition_id
        FROM sch_task_info
        WHERE compute_device_id IN
            <foreach item="item" collection="id" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
          AND status = 'running'
          AND is_delete = 1
          AND container_status IN ('running', 'exited', 'exited(0)')
    </select>
    <select id="checkTaskList" resultType="supie.webadmin.app.model.SchTaskInfo">
        SELECT ti.id,
               ti.create_time,
               ti.task_name,
               ti.container_name,
               ti.container_status,
               ti.resource_id,
               cm.container_id,
               cm.id AS containerManagerId
        FROM sch_task_info ti
                 JOIN sch_container_manager cm ON ti.id = cm.task_info_id
        WHERE ti.is_delete = 1
          AND ti.status != 'finished'
          AND ti.container_name IS NOT NULL
          AND cm.is_delete = 1
        LIMIT 50
    </select>


</mapper>
