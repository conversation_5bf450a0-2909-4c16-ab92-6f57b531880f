package supie.webadmin.app.dao;

import supie.common.core.annotation.EnableDataPerm;
import supie.common.core.base.dao.BaseDaoMapper;
import supie.webadmin.app.model.SchContainerManager;
import org.apache.ibatis.annotations.Param;
import supie.webadmin.app.model.SchTaskInfo;

import java.util.*;

/**
 * 容器管理数据操作访问接口。
 *
 * <AUTHOR> -rf .bug
 * @date 2025-05-16
 */
@EnableDataPerm
public interface SchContainerManagerMapper extends BaseDaoMapper<SchContainerManager> {

    /**
     * 批量插入对象列表。
     *
     * @param schContainerManagerList 新增对象列表。
     */
    void insertList(List<SchContainerManager> schContainerManagerList);

    /**
     * 获取分组计算后的数据对象列表。
     *
     * @param schContainerManagerFilter 主表过滤对象。
     * @param groupSelect 分组显示字段列表字符串，SELECT从句的参数。
     * @param groupBy 分组字段列表字符串，GROUP BY从句的参数。
     * @param orderBy 排序字符串，ORDER BY从句的参数。
     * @return 对象列表。
     */
    List<SchContainerManager> getGroupedSchContainerManagerList(
            @Param("schContainerManagerFilter") SchContainerManager schContainerManagerFilter,
            @Param("groupSelect") String groupSelect,
            @Param("groupBy") String groupBy,
            @Param("orderBy") String orderBy);

    /**
     * 获取过滤后的对象列表。
     *
     * @param schContainerManagerFilter 主表过滤对象。
     * @param orderBy 排序字符串，order by从句的参数。
     * @return 对象列表。
     */
    List<SchContainerManager> getSchContainerManagerList(
            @Param("schContainerManagerFilter") SchContainerManager schContainerManagerFilter, @Param("orderBy") String orderBy);


    /**
     *  通过任务id 获取容器信息
     * @param aLong 任务id
     * @return 容器信息
     */
    SchContainerManager queryContainerMsg(@Param("id") Long aLong);

    /**
     * 获取容器信息
     * @param taskId  任务id
     * @return 容器信息
     */
    SchContainerManager queryContainerPort(@Param("id") Long taskId);

    /**
     * 通过任务id 查询容器信息 容器名 容器端口
     * @param taskId 任务id
     */
   // SchContainerManager queryContainerMsg(@Param("id") Long taskId);
}
