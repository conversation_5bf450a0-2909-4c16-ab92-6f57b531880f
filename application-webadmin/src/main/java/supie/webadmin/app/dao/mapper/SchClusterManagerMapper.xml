<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="supie.webadmin.app.dao.SchClusterManagerMapper">
    <resultMap id="BaseResultMap" type="supie.webadmin.app.model.SchClusterManager">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="cluster_name" jdbcType="VARCHAR" property="clusterName"/>
        <result column="cluster_type" jdbcType="VARCHAR" property="clusterType"/>
        <result column="cluster_status" jdbcType="VARCHAR" property="clusterStatus"/>
        <result column="cluster_description" jdbcType="VARCHAR" property="clusterDescription"/>
        <result column="cluster_mark"  property="clusterMark"/>
        <result column="str_id" jdbcType="VARCHAR" property="strId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="create_user_id" jdbcType="BIGINT" property="createUserId"/>
        <result column="update_user_id" jdbcType="BIGINT" property="updateUserId"/>
        <result column="data_user_id" jdbcType="BIGINT" property="dataUserId"/>
        <result column="data_dept_id" jdbcType="BIGINT" property="dataDeptId"/>
        <result column="is_delete" jdbcType="INTEGER" property="isDelete"/>
    </resultMap>

    <insert id="insertList">
        INSERT INTO sch_cluster_manager
            (id,
            cluster_name,
            cluster_type,
            cluster_status,
            cluster_description,
            cluster_mark,
            str_id,
            update_time,
            create_time,
            create_user_id,
            update_user_id,
            data_user_id,
            data_dept_id,
            is_delete)
        VALUES
        <foreach collection="list" index="index" item="item" separator="," >
            (#{item.id},
            #{item.clusterName},
            #{item.clusterType},
            #{item.clusterStatus},
            #{item.clusterDescription},
            #{item.clusterMark},
            #{item.strId},
            #{item.updateTime},
            #{item.createTime},
            #{item.createUserId},
            #{item.updateUserId},
            #{item.dataUserId},
            #{item.dataDeptId},
            #{item.isDelete})
        </foreach>
    </insert>

    <!-- 如果有逻辑删除字段过滤，请写到这里 -->
    <sql id="filterRef">
        <!-- 这里必须加上全包名，否则当filterRef被其他Mapper.xml包含引用的时候，就会调用Mapper.xml中的该SQL片段 -->
        <include refid="supie.webadmin.app.dao.SchClusterManagerMapper.inputFilterRef"/>
        AND sch_cluster_manager.is_delete = ${@supie.common.core.constant.GlobalDeletedFlag@NORMAL}
    </sql>

    <!-- 这里仅包含调用接口输入的主表过滤条件 -->
    <sql id="inputFilterRef">
        <if test="schClusterManagerFilter != null">
            <if test="schClusterManagerFilter.id != null">
                AND sch_cluster_manager.id = #{schClusterManagerFilter.id}
            </if>
            <if test="schClusterManagerFilter.clusterName != null and schClusterManagerFilter.clusterName != ''">
                <bind name = "safeSchClusterManagerClusterName" value = "'%' + schClusterManagerFilter.clusterName + '%'" />
                AND sch_cluster_manager.cluster_name LIKE #{safeSchClusterManagerClusterName}
            </if>
            <if test="schClusterManagerFilter.clusterType != null and schClusterManagerFilter.clusterType != ''">
                <bind name = "safeSchClusterManagerClusterType" value = "'%' + schClusterManagerFilter.clusterType + '%'" />
                AND sch_cluster_manager.cluster_type LIKE #{safeSchClusterManagerClusterType}
            </if>
            <if test="schClusterManagerFilter.clusterStatus != null and schClusterManagerFilter.clusterStatus != ''">
                <bind name = "safeSchClusterManagerClusterStatus" value = "'%' + schClusterManagerFilter.clusterStatus + '%'" />
                AND sch_cluster_manager.cluster_status LIKE #{safeSchClusterManagerClusterStatus}
            </if>
            <if test="schClusterManagerFilter.clusterDescription != null and schClusterManagerFilter.clusterDescription != ''">
                <bind name = "safeSchClusterManagerClusterDescription" value = "'%' + schClusterManagerFilter.clusterDescription + '%'" />
                AND sch_cluster_manager.cluster_description LIKE #{safeSchClusterManagerClusterDescription}
            </if>
            <if test="schClusterManagerFilter.clusterMarkStart != null and schClusterManagerFilter.clusterMarkStart != ''">
                <bind name = "safeSchClusterMarkStart" value = "'%' + schClusterManagerFilter.clusterMarkStart + '%'" />
                AND sch_cluster_manager.cluster_mark &gt;= #{schClusterManagerFilter.clusterMarkStart}
            </if>
            <if test="schClusterManagerFilter.clusterMarkEnd != null and schClusterManagerFilter.clusterMarkEnd != ''">
                AND sch_cluster_manager.cluster_mark &lt;= #{schClusterManagerFilter.clusterMarkEnd}
            </if>
            <if test="schClusterManagerFilter.strId != null and schClusterManagerFilter.strId != ''">
                <bind name = "safeSchClusterManagerStrId" value = "'%' + schClusterManagerFilter.strId + '%'" />
                AND sch_cluster_manager.str_id LIKE #{safeSchClusterManagerStrId}
            </if>
            <if test="schClusterManagerFilter.updateTimeStart != null and schClusterManagerFilter.updateTimeStart != ''">
                AND sch_cluster_manager.update_time &gt;= #{schClusterManagerFilter.updateTimeStart}
            </if>
            <if test="schClusterManagerFilter.updateTimeEnd != null and schClusterManagerFilter.updateTimeEnd != ''">
                AND sch_cluster_manager.update_time &lt;= #{schClusterManagerFilter.updateTimeEnd}
            </if>
            <if test="schClusterManagerFilter.createTimeStart != null and schClusterManagerFilter.createTimeStart != ''">
                AND sch_cluster_manager.create_time &gt;= #{schClusterManagerFilter.createTimeStart}
            </if>
            <if test="schClusterManagerFilter.createTimeEnd != null and schClusterManagerFilter.createTimeEnd != ''">
                AND sch_cluster_manager.create_time &lt;= #{schClusterManagerFilter.createTimeEnd}
            </if>
            <if test="schClusterManagerFilter.createUserId != null">
                AND sch_cluster_manager.create_user_id = #{schClusterManagerFilter.createUserId}
            </if>
            <if test="schClusterManagerFilter.updateUserId != null">
                AND sch_cluster_manager.update_user_id = #{schClusterManagerFilter.updateUserId}
            </if>
            <if test="schClusterManagerFilter.dataUserId != null">
                AND sch_cluster_manager.data_user_id = #{schClusterManagerFilter.dataUserId}
            </if>
            <if test="schClusterManagerFilter.dataDeptId != null">
                AND sch_cluster_manager.data_dept_id = #{schClusterManagerFilter.dataDeptId}
            </if>
            <if test="schClusterManagerFilter.searchString != null and schClusterManagerFilter.searchString != ''">
                <bind name = "safeSchClusterManagerSearchString" value = "'%' + schClusterManagerFilter.searchString + '%'" />
                AND CONCAT(IFNULL(sch_cluster_manager.cluster_name,''), IFNULL(sch_cluster_manager.cluster_type,''), IFNULL(sch_cluster_manager.cluster_status,''), IFNULL(sch_cluster_manager.cluster_description,'')) LIKE #{safeSchClusterManagerSearchString}
            </if>
        </if>
    </sql>

    <select id="getGroupedSchClusterManagerList" resultMap="BaseResultMap" parameterType="supie.webadmin.app.model.SchClusterManager">
        SELECT * FROM
            (SELECT
                COUNT(id) id,
                ${groupSelect}
            FROM sch_cluster_manager
            <where>
                <include refid="filterRef"/>
            </where>
            GROUP BY ${groupBy}) sch_cluster_manager
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>

    <select id="getSchClusterManagerList" resultMap="BaseResultMap" parameterType="supie.webadmin.app.model.SchClusterManager">
        SELECT * FROM sch_cluster_manager
        <where>
            <include refid="filterRef"/>
        </where>
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>
</mapper>
