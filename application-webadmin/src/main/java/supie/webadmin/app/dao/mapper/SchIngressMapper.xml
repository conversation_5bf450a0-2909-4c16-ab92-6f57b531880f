<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="supie.webadmin.app.dao.SchIngressMapper">
    <resultMap id="BaseResultMap" type="supie.webadmin.app.model.SchIngress">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="ingress_name" jdbcType="VARCHAR" property="ingressName"/>
        <result column="namespace_id" jdbcType="BIGINT" property="namespaceId"/>
        <result column="cluster_id" jdbcType="BIGINT" property="clusterId"/>
        <result column="ingress_class_name" jdbcType="VARCHAR" property="ingressClassName"/>
        <result column="tls_config" jdbcType="LONGVARCHAR" property="tlsConfig"/>
        <result column="annotations" jdbcType="LONGVARCHAR" property="annotations"/>
        <result column="rules" jdbcType="LONGVARCHAR" property="rules"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="str_id" jdbcType="VARCHAR" property="strId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="create_user_id" jdbcType="BIGINT" property="createUserId"/>
        <result column="update_user_id" jdbcType="BIGINT" property="updateUserId"/>
        <result column="data_user_id" jdbcType="BIGINT" property="dataUserId"/>
        <result column="data_dept_id" jdbcType="BIGINT" property="dataDeptId"/>
        <result column="is_delete" jdbcType="INTEGER" property="isDelete"/>
    </resultMap>

    <insert id="insertList">
        INSERT INTO sch_ingress
            (id,
            ingress_name,
            namespace_id,
            cluster_id,
            ingress_class_name,
            tls_config,
            annotations,
            rules,
            status,
            str_id,
            update_time,
            create_time,
            create_user_id,
            update_user_id,
            data_user_id,
            data_dept_id,
            is_delete)
        VALUES
        <foreach collection="list" index="index" item="item" separator="," >
            (#{item.id},
            #{item.ingressName},
            #{item.namespaceId},
            #{item.clusterId},
            #{item.ingressClassName},
            #{item.tlsConfig},
            #{item.annotations},
            #{item.rules},
            #{item.status},
            #{item.strId},
            #{item.updateTime},
            #{item.createTime},
            #{item.createUserId},
            #{item.updateUserId},
            #{item.dataUserId},
            #{item.dataDeptId},
            #{item.isDelete})
        </foreach>
    </insert>

    <!-- 如果有逻辑删除字段过滤，请写到这里 -->
    <sql id="filterRef">
        <!-- 这里必须加上全包名，否则当filterRef被其他Mapper.xml包含引用的时候，就会调用Mapper.xml中的该SQL片段 -->
        <include refid="supie.webadmin.app.dao.SchIngressMapper.inputFilterRef"/>
        AND sch_ingress.is_delete = ${@supie.common.core.constant.GlobalDeletedFlag@NORMAL}
    </sql>

    <!-- 这里仅包含调用接口输入的主表过滤条件 -->
    <sql id="inputFilterRef">
        <if test="schIngressFilter != null">
            <if test="schIngressFilter.id != null">
                AND sch_ingress.id = #{schIngressFilter.id}
            </if>
            <if test="schIngressFilter.ingressName != null and schIngressFilter.ingressName != ''">
                <bind name = "safeSchIngressIngressName" value = "'%' + schIngressFilter.ingressName + '%'" />
                AND sch_ingress.ingress_name LIKE #{safeSchIngressIngressName}
            </if>
            <if test="schIngressFilter.namespaceId != null">
                AND sch_ingress.namespace_id = #{schIngressFilter.namespaceId}
            </if>
            <if test="schIngressFilter.clusterId != null">
                AND sch_ingress.cluster_id = #{schIngressFilter.clusterId}
            </if>
            <if test="schIngressFilter.ingressClassName != null and schIngressFilter.ingressClassName != ''">
                <bind name = "safeSchIngressIngressClassName" value = "'%' + schIngressFilter.ingressClassName + '%'" />
                AND sch_ingress.ingress_class_name LIKE #{safeSchIngressIngressClassName}
            </if>
            <if test="schIngressFilter.tlsConfig != null and schIngressFilter.tlsConfig != ''">
                <bind name = "safeSchIngressTlsConfig" value = "'%' + schIngressFilter.tlsConfig + '%'" />
                AND sch_ingress.tls_config LIKE #{safeSchIngressTlsConfig}
            </if>
            <if test="schIngressFilter.annotations != null and schIngressFilter.annotations != ''">
                <bind name = "safeSchIngressAnnotations" value = "'%' + schIngressFilter.annotations + '%'" />
                AND sch_ingress.annotations LIKE #{safeSchIngressAnnotations}
            </if>
            <if test="schIngressFilter.rules != null and schIngressFilter.rules != ''">
                <bind name = "safeSchIngressRules" value = "'%' + schIngressFilter.rules + '%'" />
                AND sch_ingress.rules LIKE #{safeSchIngressRules}
            </if>
            <if test="schIngressFilter.status != null and schIngressFilter.status != ''">
                <bind name = "safeSchIngressStatus" value = "'%' + schIngressFilter.status + '%'" />
                AND sch_ingress.status LIKE #{safeSchIngressStatus}
            </if>
            <if test="schIngressFilter.strId != null and schIngressFilter.strId != ''">
                <bind name = "safeSchIngressStrId" value = "'%' + schIngressFilter.strId + '%'" />
                AND sch_ingress.str_id LIKE #{safeSchIngressStrId}
            </if>
            <if test="schIngressFilter.updateTimeStart != null and schIngressFilter.updateTimeStart != ''">
                AND sch_ingress.update_time &gt;= #{schIngressFilter.updateTimeStart}
            </if>
            <if test="schIngressFilter.updateTimeEnd != null and schIngressFilter.updateTimeEnd != ''">
                AND sch_ingress.update_time &lt;= #{schIngressFilter.updateTimeEnd}
            </if>
            <if test="schIngressFilter.createTimeStart != null and schIngressFilter.createTimeStart != ''">
                AND sch_ingress.create_time &gt;= #{schIngressFilter.createTimeStart}
            </if>
            <if test="schIngressFilter.createTimeEnd != null and schIngressFilter.createTimeEnd != ''">
                AND sch_ingress.create_time &lt;= #{schIngressFilter.createTimeEnd}
            </if>
            <if test="schIngressFilter.createUserId != null">
                AND sch_ingress.create_user_id = #{schIngressFilter.createUserId}
            </if>
            <if test="schIngressFilter.updateUserId != null">
                AND sch_ingress.update_user_id = #{schIngressFilter.updateUserId}
            </if>
            <if test="schIngressFilter.dataUserId != null">
                AND sch_ingress.data_user_id = #{schIngressFilter.dataUserId}
            </if>
            <if test="schIngressFilter.dataDeptId != null">
                AND sch_ingress.data_dept_id = #{schIngressFilter.dataDeptId}
            </if>
            <if test="schIngressFilter.isDelete != null">
                AND sch_ingress.is_delete = #{schIngressFilter.isDelete}
            </if>
            <if test="schIngressFilter.searchString != null and schIngressFilter.searchString != ''">
                <bind name = "safeSchIngressSearchString" value = "'%' + schIngressFilter.searchString + '%'" />
                AND CONCAT(IFNULL(sch_ingress.ingress_name,''), IFNULL(sch_ingress.ingress_class_name,''), IFNULL(sch_ingress.tls_config,''), IFNULL(sch_ingress.annotations,''), IFNULL(sch_ingress.rules,''), IFNULL(sch_ingress.status,'')) LIKE #{safeSchIngressSearchString}
            </if>
        </if>
    </sql>

    <select id="getGroupedSchIngressList" resultMap="BaseResultMap" parameterType="supie.webadmin.app.model.SchIngress">
        SELECT * FROM
            (SELECT
                COUNT(id) id,
                ${groupSelect}
            FROM sch_ingress
            <where>
                <include refid="filterRef"/>
            </where>
            GROUP BY ${groupBy}) sch_ingress
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>

    <select id="getSchIngressList" resultMap="BaseResultMap" parameterType="supie.webadmin.app.model.SchIngress">
        SELECT * FROM sch_ingress
        <where>
            <include refid="filterRef"/>
        </where>
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>
</mapper>
