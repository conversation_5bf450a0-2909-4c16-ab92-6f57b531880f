package supie.webadmin.app.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import supie.common.log.annotation.OperationLog;
import supie.common.log.model.constant.SysOperationLogType;
import com.github.pagehelper.page.PageMethod;
import supie.webadmin.app.vo.*;
import supie.webadmin.app.dto.*;
import supie.webadmin.app.model.*;
import supie.webadmin.app.service.*;
import supie.common.core.object.*;
import supie.common.core.util.*;
import supie.common.core.constant.*;
import supie.common.core.annotation.MyRequestBody;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * 集群管理表操作控制器类。
 *
 * <AUTHOR> -rf .bug
 * @date 2025-05-16
 */
@Tag(name = "k8s集群管理表管理接口")
@Slf4j
@RestController
@RequestMapping("/admin/app/schClusterManager")
public class SchClusterManagerController {

    @Autowired
    private SchClusterManagerService schClusterManagerService;

    /**
     * 新增集群管理表数据。
     *
     * @param schClusterManagerDto 新增对象。
     * @return 应答结果对象，包含新增对象主键Id。
     */
    @ApiOperationSupport(ignoreParameters = {
            "schClusterManagerDto.id",
            "schClusterManagerDto.searchString",
            "schClusterManagerDto.clusterMarkStart",
            "schClusterManagerDto.clusterMarkEnd",
            "schClusterManagerDto.updateTimeStart",
            "schClusterManagerDto.updateTimeEnd",
            "schClusterManagerDto.createTimeStart",
            "schClusterManagerDto.createTimeEnd"})
    @SaCheckPermission("schClusterManager.add")
    @OperationLog(type = SysOperationLogType.ADD)
    @PostMapping("/add")
    public ResponseResult<Long> add(@MyRequestBody SchClusterManagerDto schClusterManagerDto) {
        String errorMessage = MyCommonUtil.getModelValidationError(schClusterManagerDto, false);
        if (errorMessage != null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, errorMessage);
        }
        SchClusterManager schClusterManager = MyModelUtil.copyTo(schClusterManagerDto, SchClusterManager.class);
        schClusterManager = schClusterManagerService.saveNew(schClusterManager);
        return ResponseResult.success(schClusterManager.getId());
    }

    /**
     * 更新集群管理表数据。
     *
     * @param schClusterManagerDto 更新对象。
     * @return 应答结果对象。
     */
    @ApiOperationSupport(ignoreParameters = {
            "schClusterManagerDto.searchString",
            "schClusterManagerDto.clusterMarkStart",
            "schClusterManagerDto.clusterMarkEnd",
            "schClusterManagerDto.updateTimeStart",
            "schClusterManagerDto.updateTimeEnd",
            "schClusterManagerDto.createTimeStart",
            "schClusterManagerDto.createTimeEnd"})
    @SaCheckPermission("schClusterManager.update")
    @OperationLog(type = SysOperationLogType.UPDATE)
    @PostMapping("/update")
    public ResponseResult<Void> update(@MyRequestBody SchClusterManagerDto schClusterManagerDto) {
        String errorMessage = MyCommonUtil.getModelValidationError(schClusterManagerDto, true);
        if (errorMessage != null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, errorMessage);
        }
        SchClusterManager schClusterManager = MyModelUtil.copyTo(schClusterManagerDto, SchClusterManager.class);
        SchClusterManager originalSchClusterManager = schClusterManagerService.getById(schClusterManager.getId());
        if (originalSchClusterManager == null) {
            // NOTE: 修改下面方括号中的话述
            errorMessage = "数据验证失败，当前 [数据] 并不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        if (!schClusterManagerService.update(schClusterManager, originalSchClusterManager)) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        return ResponseResult.success();
    }

    /**
     * 删除集群管理表数据。
     *
     * @param id 删除对象主键Id。
     * @return 应答结果对象。
     */
    @SaCheckPermission("schClusterManager.delete")
    @OperationLog(type = SysOperationLogType.DELETE)
    @PostMapping("/delete")
    public ResponseResult<Void> delete(@MyRequestBody Long id) {
        if (MyCommonUtil.existBlankArgument(id)) {
            return ResponseResult.error(ErrorCodeEnum.ARGUMENT_NULL_EXIST);
        }
        return this.doDelete(id);
    }

    /**
     * 批量删除集群管理表数据。
     *
     * @param idList 待删除对象的主键Id列表。
     * @return 应答结果对象。
     */
    @SaCheckPermission("schClusterManager.delete")
    @OperationLog(type = SysOperationLogType.DELETE_BATCH)
    @PostMapping("/deleteBatch")
    public ResponseResult<Void> deleteBatch(@MyRequestBody List<Long> idList) {
        if (MyCommonUtil.existBlankArgument(idList)) {
            return ResponseResult.error(ErrorCodeEnum.ARGUMENT_NULL_EXIST);
        }
        for (Long id : idList) {
            ResponseResult<Void> responseResult = this.doDelete(id);
            if (!responseResult.isSuccess()) {
                return responseResult;
            }
        }
        return ResponseResult.success();
    }

    /**
     * 列出符合过滤条件的集群管理表列表。
     *
     * @param schClusterManagerDtoFilter 过滤对象。
     * @param orderParam 排序参数。
     * @param pageParam 分页参数。
     * @return 应答结果对象，包含查询结果集。
     */
    @SaCheckPermission("schClusterManager.view")
    @PostMapping("/list")
    public ResponseResult<MyPageData<SchClusterManagerVo>> list(
            @MyRequestBody SchClusterManagerDto schClusterManagerDtoFilter,
            @MyRequestBody MyOrderParam orderParam,
            @MyRequestBody MyPageParam pageParam) {
        if (pageParam != null) {
            PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize(), pageParam.getCount());
        }
        SchClusterManager schClusterManagerFilter = MyModelUtil.copyTo(schClusterManagerDtoFilter, SchClusterManager.class);
        String orderBy = MyOrderParam.buildOrderBy(orderParam, SchClusterManager.class);
        List<SchClusterManager> schClusterManagerList =
                schClusterManagerService.getSchClusterManagerListWithRelation(schClusterManagerFilter, orderBy);
        return ResponseResult.success(MyPageUtil.makeResponseData(schClusterManagerList, SchClusterManagerVo.class));
    }

    /**
     * 分组列出符合过滤条件的集群管理表列表。
     *
     * @param schClusterManagerDtoFilter 过滤对象。
     * @param groupParam 分组参数。
     * @param orderParam 排序参数。
     * @param pageParam 分页参数。
     * @return 应答结果对象，包含查询结果集。
     */
    @SaCheckPermission("schClusterManager.view")
    @PostMapping("/listWithGroup")
    public ResponseResult<MyPageData<SchClusterManagerVo>> listWithGroup(
            @MyRequestBody SchClusterManagerDto schClusterManagerDtoFilter,
            @MyRequestBody(required = true) MyGroupParam groupParam,
            @MyRequestBody MyOrderParam orderParam,
            @MyRequestBody MyPageParam pageParam) {
        String orderBy = MyOrderParam.buildOrderBy(orderParam, SchClusterManager.class, false);
        groupParam = MyGroupParam.buildGroupBy(groupParam, SchClusterManager.class);
        if (groupParam == null) {
            return ResponseResult.error(
                    ErrorCodeEnum.INVALID_ARGUMENT_FORMAT, "数据参数错误，分组参数不能为空！");
        }
        if (pageParam != null) {
            PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize(), pageParam.getCount());
        }
        SchClusterManager filter = MyModelUtil.copyTo(schClusterManagerDtoFilter, SchClusterManager.class);
        MyGroupCriteria criteria = groupParam.getGroupCriteria();
        List<SchClusterManager> resultList = schClusterManagerService.getGroupedSchClusterManagerListWithRelation(
                filter, criteria.getGroupSelect(), criteria.getGroupBy(), orderBy);
        // 分页连同对象数据转换copy工作，下面的方法一并完成。
        return ResponseResult.success(MyPageUtil.makeResponseData(resultList, SchClusterManagerVo.class));
    }

    /**
     * 查看指定集群管理表对象详情。
     *
     * @param id 指定对象主键Id。
     * @return 应答结果对象，包含对象详情。
     */
    @SaCheckPermission("schClusterManager.view")
    @GetMapping("/view")
    public ResponseResult<SchClusterManagerVo> view(@RequestParam Long id) {
        SchClusterManager schClusterManager = schClusterManagerService.getByIdWithRelation(id, MyRelationParam.full());
        if (schClusterManager == null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        SchClusterManagerVo schClusterManagerVo = MyModelUtil.copyTo(schClusterManager, SchClusterManagerVo.class);
        return ResponseResult.success(schClusterManagerVo);
    }

    private ResponseResult<Void> doDelete(Long id) {
        String errorMessage;
        // 验证关联Id的数据合法性
        SchClusterManager originalSchClusterManager = schClusterManagerService.getById(id);
        if (originalSchClusterManager == null) {
            // NOTE: 修改下面方括号中的话述
            errorMessage = "数据验证失败，当前 [对象] 并不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        if (!schClusterManagerService.remove(id)) {
            errorMessage = "数据操作失败，删除的对象不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        return ResponseResult.success();
    }

    /**
     * 集群统计。
     *
     * @return 应答结果对象，包含对象详情。
     */
    @GetMapping("/clusterStatistics")
    public ResponseResult<Map<String,Object>> clusterStatistics() {
        return ResponseResult.success(schClusterManagerService.clusterStatistics());
    }
}
