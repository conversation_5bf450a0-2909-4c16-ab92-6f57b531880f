package supie.webadmin.app.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import supie.common.log.annotation.OperationLog;
import supie.common.log.model.constant.SysOperationLogType;
import com.github.pagehelper.page.PageMethod;
import supie.webadmin.app.vo.*;
import supie.webadmin.app.dto.*;
import supie.webadmin.app.model.*;
import supie.webadmin.app.service.*;
import supie.common.core.object.*;
import supie.common.core.util.*;
import supie.common.core.constant.*;
import supie.common.core.annotation.MyRequestBody;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * 任务审批记录表操作控制器类。
 *
 * <AUTHOR> -rf .bug
 * @date 2025-05-16
 */
@Tag(name = "任务审批记录表管理接口")
@Slf4j
@RestController
@RequestMapping("/admin/app/schTaskApproval")
public class SchTaskApprovalController {

    @Autowired
    private SchTaskApprovalService schTaskApprovalService;

    /**
     * 新增任务审批记录表数据。
     *
     * @param schTaskApprovalDto 新增对象。
     * @return 应答结果对象，包含新增对象主键Id。
     */
    @ApiOperationSupport(ignoreParameters = {
            "schTaskApprovalDto.id",
            "schTaskApprovalDto.searchString",
            "schTaskApprovalDto.updateTimeStart",
            "schTaskApprovalDto.updateTimeEnd",
            "schTaskApprovalDto.createTimeStart",
            "schTaskApprovalDto.createTimeEnd",
            "schTaskApprovalDto.decisionTimeStart",
            "schTaskApprovalDto.decisionTimeEnd"})
    @SaCheckPermission("schTaskApproval.add")
    @OperationLog(type = SysOperationLogType.ADD)
    @PostMapping("/add")
    public ResponseResult<Long> add(@MyRequestBody SchTaskApprovalDto schTaskApprovalDto) {
        String errorMessage = MyCommonUtil.getModelValidationError(schTaskApprovalDto, false);
        if (errorMessage != null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, errorMessage);
        }
        SchTaskApproval schTaskApproval = MyModelUtil.copyTo(schTaskApprovalDto, SchTaskApproval.class);
        schTaskApproval = schTaskApprovalService.saveNew(schTaskApproval);
        return ResponseResult.success(schTaskApproval.getId());
    }

    /**
     * 更新任务审批记录表数据。
     *
     * @param schTaskApprovalDto 更新对象。
     * @return 应答结果对象。
     */
    @ApiOperationSupport(ignoreParameters = {
            "schTaskApprovalDto.searchString",
            "schTaskApprovalDto.updateTimeStart",
            "schTaskApprovalDto.updateTimeEnd",
            "schTaskApprovalDto.createTimeStart",
            "schTaskApprovalDto.createTimeEnd",
            "schTaskApprovalDto.decisionTimeStart",
            "schTaskApprovalDto.decisionTimeEnd"})
    @SaCheckPermission("schTaskApproval.update")
    @OperationLog(type = SysOperationLogType.UPDATE)
    @PostMapping("/update")
    public ResponseResult<Void> update(@MyRequestBody SchTaskApprovalDto schTaskApprovalDto) {
        String errorMessage = MyCommonUtil.getModelValidationError(schTaskApprovalDto, true);
        if (errorMessage != null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, errorMessage);
        }
        SchTaskApproval schTaskApproval = MyModelUtil.copyTo(schTaskApprovalDto, SchTaskApproval.class);
        SchTaskApproval originalSchTaskApproval = schTaskApprovalService.getById(schTaskApproval.getId());
        if (originalSchTaskApproval == null) {
            // NOTE: 修改下面方括号中的话述
            errorMessage = "数据验证失败，当前 [数据] 并不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        if (!schTaskApprovalService.update(schTaskApproval, originalSchTaskApproval)) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        return ResponseResult.success();
    }

    /**
     * 删除任务审批记录表数据。
     *
     * @param id 删除对象主键Id。
     * @return 应答结果对象。
     */
    @SaCheckPermission("schTaskApproval.delete")
    @OperationLog(type = SysOperationLogType.DELETE)
    @PostMapping("/delete")
    public ResponseResult<Void> delete(@MyRequestBody Long id) {
        if (MyCommonUtil.existBlankArgument(id)) {
            return ResponseResult.error(ErrorCodeEnum.ARGUMENT_NULL_EXIST);
        }
        return this.doDelete(id);
    }

    /**
     * 批量删除任务审批记录表数据。
     *
     * @param idList 待删除对象的主键Id列表。
     * @return 应答结果对象。
     */
    @SaCheckPermission("schTaskApproval.delete")
    @OperationLog(type = SysOperationLogType.DELETE_BATCH)
    @PostMapping("/deleteBatch")
    public ResponseResult<Void> deleteBatch(@MyRequestBody List<Long> idList) {
        if (MyCommonUtil.existBlankArgument(idList)) {
            return ResponseResult.error(ErrorCodeEnum.ARGUMENT_NULL_EXIST);
        }
        for (Long id : idList) {
            ResponseResult<Void> responseResult = this.doDelete(id);
            if (!responseResult.isSuccess()) {
                return responseResult;
            }
        }
        return ResponseResult.success();
    }

    /**
     * 列出符合过滤条件的任务审批记录表列表。
     *
     * @param schTaskApprovalDtoFilter 过滤对象。
     * @param orderParam 排序参数。
     * @param pageParam 分页参数。
     * @return 应答结果对象，包含查询结果集。
     */
    @SaCheckPermission("schTaskApproval.view")
    @PostMapping("/list")
    public ResponseResult<MyPageData<SchTaskApprovalVo>> list(
            @MyRequestBody SchTaskApprovalDto schTaskApprovalDtoFilter,
            @MyRequestBody MyOrderParam orderParam,
            @MyRequestBody MyPageParam pageParam) {
        if (pageParam != null) {
            PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize(), pageParam.getCount());
        }
        SchTaskApproval schTaskApprovalFilter = MyModelUtil.copyTo(schTaskApprovalDtoFilter, SchTaskApproval.class);
        String orderBy = MyOrderParam.buildOrderBy(orderParam, SchTaskApproval.class);
        List<SchTaskApproval> schTaskApprovalList =
                schTaskApprovalService.getSchTaskApprovalListWithRelation(schTaskApprovalFilter, orderBy);
        return ResponseResult.success(MyPageUtil.makeResponseData(schTaskApprovalList, SchTaskApprovalVo.class));
    }

    /**
     * 分组列出符合过滤条件的任务审批记录表列表。
     *
     * @param schTaskApprovalDtoFilter 过滤对象。
     * @param groupParam 分组参数。
     * @param orderParam 排序参数。
     * @param pageParam 分页参数。
     * @return 应答结果对象，包含查询结果集。
     */
    @SaCheckPermission("schTaskApproval.view")
    @PostMapping("/listWithGroup")
    public ResponseResult<MyPageData<SchTaskApprovalVo>> listWithGroup(
            @MyRequestBody SchTaskApprovalDto schTaskApprovalDtoFilter,
            @MyRequestBody(required = true) MyGroupParam groupParam,
            @MyRequestBody MyOrderParam orderParam,
            @MyRequestBody MyPageParam pageParam) {
        String orderBy = MyOrderParam.buildOrderBy(orderParam, SchTaskApproval.class, false);
        groupParam = MyGroupParam.buildGroupBy(groupParam, SchTaskApproval.class);
        if (groupParam == null) {
            return ResponseResult.error(
                    ErrorCodeEnum.INVALID_ARGUMENT_FORMAT, "数据参数错误，分组参数不能为空！");
        }
        if (pageParam != null) {
            PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize(), pageParam.getCount());
        }
        SchTaskApproval filter = MyModelUtil.copyTo(schTaskApprovalDtoFilter, SchTaskApproval.class);
        MyGroupCriteria criteria = groupParam.getGroupCriteria();
        List<SchTaskApproval> resultList = schTaskApprovalService.getGroupedSchTaskApprovalListWithRelation(
                filter, criteria.getGroupSelect(), criteria.getGroupBy(), orderBy);
        // 分页连同对象数据转换copy工作，下面的方法一并完成。
        return ResponseResult.success(MyPageUtil.makeResponseData(resultList, SchTaskApprovalVo.class));
    }

    /**
     * 查看指定任务审批记录表对象详情。
     *
     * @param id 指定对象主键Id。
     * @return 应答结果对象，包含对象详情。
     */
    @SaCheckPermission("schTaskApproval.view")
    @GetMapping("/view")
    public ResponseResult<SchTaskApprovalVo> view(@RequestParam Long id) {
        SchTaskApproval schTaskApproval = schTaskApprovalService.getByIdWithRelation(id, MyRelationParam.full());
        if (schTaskApproval == null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        SchTaskApprovalVo schTaskApprovalVo = MyModelUtil.copyTo(schTaskApproval, SchTaskApprovalVo.class);
        return ResponseResult.success(schTaskApprovalVo);
    }

    private ResponseResult<Void> doDelete(Long id) {
        String errorMessage;
        // 验证关联Id的数据合法性
        SchTaskApproval originalSchTaskApproval = schTaskApprovalService.getById(id);
        if (originalSchTaskApproval == null) {
            // NOTE: 修改下面方括号中的话述
            errorMessage = "数据验证失败，当前 [对象] 并不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        if (!schTaskApprovalService.remove(id)) {
            errorMessage = "数据操作失败，删除的对象不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        return ResponseResult.success();
    }
}
