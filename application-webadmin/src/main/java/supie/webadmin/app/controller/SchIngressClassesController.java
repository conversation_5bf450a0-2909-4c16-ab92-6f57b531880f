package supie.webadmin.app.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import supie.common.log.annotation.OperationLog;
import supie.common.log.model.constant.SysOperationLogType;
import com.github.pagehelper.page.PageMethod;
import supie.webadmin.app.vo.*;
import supie.webadmin.app.dto.*;
import supie.webadmin.app.model.*;
import supie.webadmin.app.service.*;
import supie.common.core.object.*;
import supie.common.core.util.*;
import supie.common.core.constant.*;
import supie.common.core.annotation.MyRequestBody;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * k8s Ingress类表操作控制器类。
 *
 * <AUTHOR> -rf .bug
 * @date 2025-05-16
 */
@Tag(name = "k8s Ingress类表管理接口")
@Slf4j
@RestController
@RequestMapping("/admin/app/schIngressClasses")
public class SchIngressClassesController {

    @Autowired
    private SchIngressClassesService schIngressClassesService;

    /**
     * 新增k8s Ingress类表数据。
     *
     * @param schIngressClassesDto 新增对象。
     * @return 应答结果对象，包含新增对象主键Id。
     */
    @ApiOperationSupport(ignoreParameters = {
            "schIngressClassesDto.id",
            "schIngressClassesDto.searchString",
            "schIngressClassesDto.updateTimeStart",
            "schIngressClassesDto.updateTimeEnd",
            "schIngressClassesDto.createTimeStart",
            "schIngressClassesDto.createTimeEnd"})
    @SaCheckPermission("schIngressClasses.add")
    @OperationLog(type = SysOperationLogType.ADD)
    @PostMapping("/add")
    public ResponseResult<String> add(@MyRequestBody SchIngressClassesDto schIngressClassesDto) {
        String errorMessage = MyCommonUtil.getModelValidationError(schIngressClassesDto, false);
        if (errorMessage != null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, errorMessage);
        }
        SchIngressClasses schIngressClasses = MyModelUtil.copyTo(schIngressClassesDto, SchIngressClasses.class);
        schIngressClasses = schIngressClassesService.saveNew(schIngressClasses);
        return ResponseResult.success(schIngressClasses.getId());
    }

    /**
     * 更新k8s Ingress类表数据。
     *
     * @param schIngressClassesDto 更新对象。
     * @return 应答结果对象。
     */
    @ApiOperationSupport(ignoreParameters = {
            "schIngressClassesDto.searchString",
            "schIngressClassesDto.updateTimeStart",
            "schIngressClassesDto.updateTimeEnd",
            "schIngressClassesDto.createTimeStart",
            "schIngressClassesDto.createTimeEnd"})
    @SaCheckPermission("schIngressClasses.update")
    @OperationLog(type = SysOperationLogType.UPDATE)
    @PostMapping("/update")
    public ResponseResult<Void> update(@MyRequestBody SchIngressClassesDto schIngressClassesDto) {
        String errorMessage = MyCommonUtil.getModelValidationError(schIngressClassesDto, true);
        if (errorMessage != null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, errorMessage);
        }
        SchIngressClasses schIngressClasses = MyModelUtil.copyTo(schIngressClassesDto, SchIngressClasses.class);
        SchIngressClasses originalSchIngressClasses = schIngressClassesService.getById(schIngressClasses.getId());
        if (originalSchIngressClasses == null) {
            // NOTE: 修改下面方括号中的话述
            errorMessage = "数据验证失败，当前 [数据] 并不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        if (!schIngressClassesService.update(schIngressClasses, originalSchIngressClasses)) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        return ResponseResult.success();
    }

    /**
     * 删除k8s Ingress类表数据。
     *
     * @param id 删除对象主键Id。
     * @return 应答结果对象。
     */
    @SaCheckPermission("schIngressClasses.delete")
    @OperationLog(type = SysOperationLogType.DELETE)
    @PostMapping("/delete")
    public ResponseResult<Void> delete(@MyRequestBody String id) {
        if (MyCommonUtil.existBlankArgument(id)) {
            return ResponseResult.error(ErrorCodeEnum.ARGUMENT_NULL_EXIST);
        }
        return this.doDelete(id);
    }

    /**
     * 批量删除k8s Ingress类表数据。
     *
     * @param idList 待删除对象的主键Id列表。
     * @return 应答结果对象。
     */
    @SaCheckPermission("schIngressClasses.delete")
    @OperationLog(type = SysOperationLogType.DELETE_BATCH)
    @PostMapping("/deleteBatch")
    public ResponseResult<Void> deleteBatch(@MyRequestBody List<String> idList) {
        if (MyCommonUtil.existBlankArgument(idList)) {
            return ResponseResult.error(ErrorCodeEnum.ARGUMENT_NULL_EXIST);
        }
        for (String id : idList) {
            ResponseResult<Void> responseResult = this.doDelete(id);
            if (!responseResult.isSuccess()) {
                return responseResult;
            }
        }
        return ResponseResult.success();
    }

    /**
     * 列出符合过滤条件的k8s Ingress类表列表。
     *
     * @param schIngressClassesDtoFilter 过滤对象。
     * @param orderParam 排序参数。
     * @param pageParam 分页参数。
     * @return 应答结果对象，包含查询结果集。
     */
    @SaCheckPermission("schIngressClasses.view")
    @PostMapping("/list")
    public ResponseResult<MyPageData<SchIngressClassesVo>> list(
            @MyRequestBody SchIngressClassesDto schIngressClassesDtoFilter,
            @MyRequestBody MyOrderParam orderParam,
            @MyRequestBody MyPageParam pageParam) {
        if (pageParam != null) {
            PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize(), pageParam.getCount());
        }
        SchIngressClasses schIngressClassesFilter = MyModelUtil.copyTo(schIngressClassesDtoFilter, SchIngressClasses.class);
        String orderBy = MyOrderParam.buildOrderBy(orderParam, SchIngressClasses.class);
        List<SchIngressClasses> schIngressClassesList =
                schIngressClassesService.getSchIngressClassesListWithRelation(schIngressClassesFilter, orderBy);
        return ResponseResult.success(MyPageUtil.makeResponseData(schIngressClassesList, SchIngressClassesVo.class));
    }

    /**
     * 分组列出符合过滤条件的k8s Ingress类表列表。
     *
     * @param schIngressClassesDtoFilter 过滤对象。
     * @param groupParam 分组参数。
     * @param orderParam 排序参数。
     * @param pageParam 分页参数。
     * @return 应答结果对象，包含查询结果集。
     */
    @SaCheckPermission("schIngressClasses.view")
    @PostMapping("/listWithGroup")
    public ResponseResult<MyPageData<SchIngressClassesVo>> listWithGroup(
            @MyRequestBody SchIngressClassesDto schIngressClassesDtoFilter,
            @MyRequestBody(required = true) MyGroupParam groupParam,
            @MyRequestBody MyOrderParam orderParam,
            @MyRequestBody MyPageParam pageParam) {
        String orderBy = MyOrderParam.buildOrderBy(orderParam, SchIngressClasses.class, false);
        groupParam = MyGroupParam.buildGroupBy(groupParam, SchIngressClasses.class);
        if (groupParam == null) {
            return ResponseResult.error(
                    ErrorCodeEnum.INVALID_ARGUMENT_FORMAT, "数据参数错误，分组参数不能为空！");
        }
        if (pageParam != null) {
            PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize(), pageParam.getCount());
        }
        SchIngressClasses filter = MyModelUtil.copyTo(schIngressClassesDtoFilter, SchIngressClasses.class);
        MyGroupCriteria criteria = groupParam.getGroupCriteria();
        List<SchIngressClasses> resultList = schIngressClassesService.getGroupedSchIngressClassesListWithRelation(
                filter, criteria.getGroupSelect(), criteria.getGroupBy(), orderBy);
        // 分页连同对象数据转换copy工作，下面的方法一并完成。
        return ResponseResult.success(MyPageUtil.makeResponseData(resultList, SchIngressClassesVo.class));
    }

    /**
     * 查看指定k8s Ingress类表对象详情。
     *
     * @param id 指定对象主键Id。
     * @return 应答结果对象，包含对象详情。
     */
    @SaCheckPermission("schIngressClasses.view")
    @GetMapping("/view")
    public ResponseResult<SchIngressClassesVo> view(@RequestParam String id) {
        SchIngressClasses schIngressClasses = schIngressClassesService.getByIdWithRelation(id, MyRelationParam.full());
        if (schIngressClasses == null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        SchIngressClassesVo schIngressClassesVo = MyModelUtil.copyTo(schIngressClasses, SchIngressClassesVo.class);
        return ResponseResult.success(schIngressClassesVo);
    }

    private ResponseResult<Void> doDelete(String id) {
        String errorMessage;
        // 验证关联Id的数据合法性
        SchIngressClasses originalSchIngressClasses = schIngressClassesService.getById(id);
        if (originalSchIngressClasses == null) {
            // NOTE: 修改下面方括号中的话述
            errorMessage = "数据验证失败，当前 [对象] 并不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        if (!schIngressClassesService.remove(id)) {
            errorMessage = "数据操作失败，删除的对象不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        return ResponseResult.success();
    }
}
