package supie.webadmin.app.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.annotation.Resource;
import supie.common.log.annotation.OperationLog;
import supie.common.log.model.constant.SysOperationLogType;
import com.github.pagehelper.page.PageMethod;
import supie.webadmin.app.dao.SchResourcePoolMemberMapper;
import supie.webadmin.app.vo.*;
import supie.webadmin.app.dto.*;
import supie.webadmin.app.model.*;
import supie.webadmin.app.service.*;
import supie.common.core.object.*;
import supie.common.core.util.*;
import supie.common.core.constant.*;
import supie.common.core.annotation.MyRequestBody;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * 资源池信息表操作控制器类。
 *
 * <AUTHOR> -rf .bug
 * @date 2025-05-16
 */
@Tag(name = "资源池信息表管理接口")
@Slf4j
@RestController
@RequestMapping("/admin/app/schResourcePool")
public class SchResourcePoolController {

    @Autowired
    private SchResourcePoolService schResourcePoolService;

    @Resource
    private SchResourcePoolMemberMapper schResourcePoolMemberMapper;

    /**
     * 新增资源池信息表数据。
     *
     * @param schResourcePoolDto 新增对象。
     * @return 应答结果对象，包含新增对象主键Id。
     */
    @ApiOperationSupport(ignoreParameters = {
            "schResourcePoolDto.id",
            "schResourcePoolDto.searchString",
            "schResourcePoolDto.updateTimeStart",
            "schResourcePoolDto.updateTimeEnd",
            "schResourcePoolDto.createTimeStart",
            "schResourcePoolDto.createTimeEnd"})
    @SaCheckPermission("schResourcePool.add")
    @OperationLog(type = SysOperationLogType.ADD)
    @PostMapping("/add")
    public ResponseResult<Long> add(@MyRequestBody SchResourcePoolDto schResourcePoolDto) {
        String errorMessage = MyCommonUtil.getModelValidationError(schResourcePoolDto, false);
        if (errorMessage != null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, errorMessage);
        }
        SchResourcePool schResourcePool = MyModelUtil.copyTo(schResourcePoolDto, SchResourcePool.class);
        schResourcePool = schResourcePoolService.saveNew(schResourcePool);
        return ResponseResult.success(schResourcePool.getId());
    }

    /**
     * 更新资源池信息表数据。
     *
     * @param schResourcePoolDto 更新对象。
     * @return 应答结果对象。
     */
    @ApiOperationSupport(ignoreParameters = {
            "schResourcePoolDto.searchString",
            "schResourcePoolDto.updateTimeStart",
            "schResourcePoolDto.updateTimeEnd",
            "schResourcePoolDto.createTimeStart",
            "schResourcePoolDto.createTimeEnd"})
    @SaCheckPermission("schResourcePool.update")
    @OperationLog(type = SysOperationLogType.UPDATE)
    @PostMapping("/update")
    public ResponseResult<Void> update(@MyRequestBody SchResourcePoolDto schResourcePoolDto) {
        String errorMessage = MyCommonUtil.getModelValidationError(schResourcePoolDto, true);
        if (errorMessage != null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, errorMessage);
        }
        SchResourcePool schResourcePool = MyModelUtil.copyTo(schResourcePoolDto, SchResourcePool.class);
        SchResourcePool originalSchResourcePool = schResourcePoolService.getById(schResourcePool.getId());
        if (originalSchResourcePool == null) {
            // NOTE: 修改下面方括号中的话述
            errorMessage = "数据验证失败，当前 [数据] 并不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        if (!schResourcePoolService.update(schResourcePool, originalSchResourcePool)) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        return ResponseResult.success();
    }

    /**
     * 删除资源池信息表数据。
     *
     * @param id 删除对象主键Id。
     * @return 应答结果对象。
     */
    @SaCheckPermission("schResourcePool.delete")
    @OperationLog(type = SysOperationLogType.DELETE)
    @PostMapping("/delete")
    public ResponseResult<Void> delete(@MyRequestBody Long id) {
        if (MyCommonUtil.existBlankArgument(id)) {
            return ResponseResult.error(ErrorCodeEnum.ARGUMENT_NULL_EXIST);
        }
        return this.doDelete(id);
    }

    /**
     * 批量删除资源池信息表数据。
     *
     * @param idList 待删除对象的主键Id列表。
     * @return 应答结果对象。
     */
    @SaCheckPermission("schResourcePool.delete")
    @OperationLog(type = SysOperationLogType.DELETE_BATCH)
    @PostMapping("/deleteBatch")
    public ResponseResult<Void> deleteBatch(@MyRequestBody List<Long> idList) {
        if (MyCommonUtil.existBlankArgument(idList)) {
            return ResponseResult.error(ErrorCodeEnum.ARGUMENT_NULL_EXIST);
        }
        for (Long id : idList) {
            ResponseResult<Void> responseResult = this.doDelete(id);
            if (!responseResult.isSuccess()) {
                return responseResult;
            }
        }
        return ResponseResult.success();
    }

    /**
     * 列出符合过滤条件的资源池信息表列表。
     *
     * @param schResourcePoolDtoFilter 过滤对象。
     * @param orderParam 排序参数。
     * @param pageParam 分页参数。
     * @return 应答结果对象，包含查询结果集。
     */
    @SaCheckPermission("schResourcePool.view")
    @PostMapping("/list")
    public ResponseResult<MyPageData<SchResourcePoolVo>> list(
            @MyRequestBody SchResourcePoolDto schResourcePoolDtoFilter,
            @MyRequestBody MyOrderParam orderParam,
            @MyRequestBody MyPageParam pageParam) {
        if (pageParam != null) {
            PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize(), pageParam.getCount());
        }
        SchResourcePool schResourcePoolFilter = MyModelUtil.copyTo(schResourcePoolDtoFilter, SchResourcePool.class);
        String orderBy = MyOrderParam.buildOrderBy(orderParam, SchResourcePool.class);
        List<SchResourcePool> schResourcePoolList =
                schResourcePoolService.getSchResourcePoolListWithRelation(schResourcePoolFilter, orderBy);

        return ResponseResult.success(MyPageUtil.makeResponseData(schResourcePoolList, SchResourcePoolVo.class));
    }

    /**
     * 列出符合过滤条件的资源池信息表列表。
     *
     * @param schResourcePoolDtoFilter 过滤对象。
     * @param orderParam 排序参数。
     * @param pageParam 分页参数。
     * @return 应答结果对象，包含查询结果集。
     */
    @SaCheckPermission("schResourcePool.view")
    @PostMapping("/listV1")
    public ResponseResult<MyPageData<SchResourcePoolVo>> listV1(
            @MyRequestBody SchResourcePoolDto schResourcePoolDtoFilter,
            @MyRequestBody MyOrderParam orderParam,
            @MyRequestBody MyPageParam pageParam) {
        if (pageParam != null) {
            PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize(), pageParam.getCount());
        }
        SchResourcePool schResourcePoolFilter = MyModelUtil.copyTo(schResourcePoolDtoFilter, SchResourcePool.class);
        String orderBy = MyOrderParam.buildOrderBy(orderParam, SchResourcePool.class);
        List<SchResourcePool> schResourcePoolList =
                schResourcePoolService.getSchResourcePoolListWithRelationV1(schResourcePoolFilter, orderBy);

        return ResponseResult.success(MyPageUtil.makeResponseData(schResourcePoolList, SchResourcePoolVo.class));
    }

    /**
     * 分组列出符合过滤条件的资源池信息表列表。
     *
     * @param schResourcePoolDtoFilter 过滤对象。
     * @param groupParam 分组参数。
     * @param orderParam 排序参数。
     * @param pageParam 分页参数。
     * @return 应答结果对象，包含查询结果集。
     */
    @SaCheckPermission("schResourcePool.view")
    @PostMapping("/listWithGroup")
    public ResponseResult<MyPageData<SchResourcePoolVo>> listWithGroup(
            @MyRequestBody SchResourcePoolDto schResourcePoolDtoFilter,
            @MyRequestBody(required = true) MyGroupParam groupParam,
            @MyRequestBody MyOrderParam orderParam,
            @MyRequestBody MyPageParam pageParam) {
        String orderBy = MyOrderParam.buildOrderBy(orderParam, SchResourcePool.class, false);
        groupParam = MyGroupParam.buildGroupBy(groupParam, SchResourcePool.class);
        if (groupParam == null) {
            return ResponseResult.error(
                    ErrorCodeEnum.INVALID_ARGUMENT_FORMAT, "数据参数错误，分组参数不能为空！");
        }
        if (pageParam != null) {
            PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize(), pageParam.getCount());
        }
        SchResourcePool filter = MyModelUtil.copyTo(schResourcePoolDtoFilter, SchResourcePool.class);
        MyGroupCriteria criteria = groupParam.getGroupCriteria();
        List<SchResourcePool> resultList = schResourcePoolService.getGroupedSchResourcePoolListWithRelation(
                filter, criteria.getGroupSelect(), criteria.getGroupBy(), orderBy);
        // 分页连同对象数据转换copy工作，下面的方法一并完成。
        return ResponseResult.success(MyPageUtil.makeResponseData(resultList, SchResourcePoolVo.class));
    }

    /**
     * 查看指定资源池信息表对象详情。
     *
     * @param id 指定对象主键Id。
     * @return 应答结果对象，包含对象详情。
     */
    @SaCheckPermission("schResourcePool.view")
    @GetMapping("/view")
    public ResponseResult<SchResourcePoolVo> view(@RequestParam Long id) {
        SchResourcePool schResourcePool = schResourcePoolService.getByIdWithRelation(id, MyRelationParam.full());
        if (schResourcePool == null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        Map<String, Object> map = schResourcePoolService.resourcePoolStatistics(schResourcePool);
        schResourcePool.setResourcePoolStatistics(map);
        SchResourcePoolVo schResourcePoolVo = MyModelUtil.copyTo(schResourcePool, SchResourcePoolVo.class);
        return ResponseResult.success(schResourcePoolVo);
    }

    private ResponseResult<Void> doDelete(Long id) {
        String errorMessage;
        // 验证关联Id的数据合法性
        SchResourcePool originalSchResourcePool = schResourcePoolService.getById(id);
        if (originalSchResourcePool == null) {
            // NOTE: 修改下面方括号中的话述
            errorMessage = "数据验证失败，当前 [对象] 并不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        if (!schResourcePoolService.remove(id)) {
            errorMessage = "数据操作失败，删除的对象不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        // 关联关系删除
        schResourcePoolMemberMapper.deletePoolRelation(id);
        return ResponseResult.success();
    }
    /**
     * 资源池总览
     * @return 应答结果对象，包含对象详情。
     */
    @Operation(summary = "资源池总览")//资源池总览
    @GetMapping("/resourcePoolTotal")
    public ResponseResult<Map<String, Object>> resourcePoolTotal() {
        Map<String, Object> resourcePoolInfo = schResourcePoolService.getResourcePoolInfo();
//        if(resourcePoolInfo == null){
//            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
//        }
        return ResponseResult.success(resourcePoolInfo);
    }



    /**
     * 资源池统计。
     *
     * @param
     */
    @PostMapping("/resourcePoolStatistics")//资源池管理
    public ResponseResult<Map<String, Object>> resourcePoolStatistics(@MyRequestBody Long schResourcePoolId) {
        if(schResourcePoolId==null){
            return ResponseResult.error(ErrorCodeEnum.ARGUMENT_NULL_EXIST);
        }
        SchResourcePool schResourcePool = schResourcePoolService.getById(schResourcePoolId);
        if(schResourcePool==null){
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        return ResponseResult.success(schResourcePoolService.resourcePoolStatistics(schResourcePool));
    }
}
