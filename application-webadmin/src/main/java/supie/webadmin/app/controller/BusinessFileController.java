package supie.webadmin.app.controller;


import cn.hutool.core.util.ReflectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.page.PageMethod;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import supie.common.core.annotation.DisableDataFilter;
import supie.common.core.annotation.MyRequestBody;
import supie.common.core.constant.ErrorCodeEnum;
import supie.common.core.object.*;
import supie.common.core.upload.BaseUpDownloader;
import supie.common.core.upload.UpDownloaderFactory;
import supie.common.core.upload.UploadResponseInfo;
import supie.common.core.upload.UploadStoreInfo;
import supie.common.core.util.MyCommonUtil;
import supie.common.core.util.MyModelUtil;
import supie.common.core.util.MyPageUtil;
import supie.common.log.annotation.OperationLog;
import supie.common.log.model.constant.SysOperationLogType;
import supie.common.redis.cache.SessionCacheHelper;
import supie.common.sequence.wrapper.IdGeneratorWrapper;
import supie.webadmin.app.dao.AppBusinessFileMapper;
import supie.webadmin.app.dto.BusinessFileDto;
import supie.webadmin.app.model.BusinessFile;
import supie.webadmin.app.service.AppBusinessFileService;
import supie.webadmin.app.vo.BusinessFileVo;
import supie.webadmin.config.ApplicationConfig;

import java.io.IOException;

import java.util.List;

/**
 * 业务附件表操作控制器类。
 * https://www.youtube.com/watch?v=gfDj95CENGc
 * <AUTHOR> -rf .bug
 * @date 2025-02-17
 */
@Tag(name = "业务附件表管理接口")
@Slf4j
@RestController(value = "appBusinessFileController")
@RequestMapping("/admin/app/businessFile")
public class BusinessFileController {

    @Resource(name = "appBusinessFileService")
    private AppBusinessFileService     appBusinessFileService;
    @Resource
    private ApplicationConfig appConfig;
    @Resource
    private SessionCacheHelper cacheHelper;
    @Resource
    private UpDownloaderFactory upDownloaderFactory;
    private AppBusinessFileMapper appBusinessFileMapper;


    /**
     * 新增业务附件表数据。
     *
     * @param businessFileDto 新增对象。
     * @return 应答结果对象，包含新增对象主键Id。
     */

    @ApiOperationSupport(ignoreParameters = {
            "businessFileDto.id",
            "businessFileDto.searchString",
            "businessFileDto.updateTimeStart",
            "businessFileDto.updateTimeEnd",
            "businessFileDto.createTimeStart",
            "businessFileDto.createTimeEnd",
            "businessFileDto.fileSizeStart",
            "businessFileDto.fileSizeEnd"})
    @OperationLog(type = SysOperationLogType.ADD)
    @PostMapping("/add")
    public ResponseResult<Long> add(@MyRequestBody BusinessFileDto businessFileDto) {
        String errorMessage = MyCommonUtil.getModelValidationError(businessFileDto, false);
        if (errorMessage != null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, errorMessage);
        }
        BusinessFile businessFile = MyModelUtil.copyTo(businessFileDto, BusinessFile.class);
        businessFile = appBusinessFileService.saveNew(businessFile);
        return ResponseResult.success(businessFile.getId());

    }

    /**
     * 更新业务附件表数据。
     *
     * @param businessFileDto 更新对象。
     * @return 应答结果对象。
     */
    @ApiOperationSupport(ignoreParameters = {
            "businessFileDto.searchString",
            "businessFileDto.updateTimeStart",
            "businessFileDto.updateTimeEnd",
            "businessFileDto.createTimeStart",
            "businessFileDto.createTimeEnd",
            "businessFileDto.fileSizeStart",
            "businessFileDto.fileSizeEnd"})
    @OperationLog(type = SysOperationLogType.UPDATE)
    @PostMapping("/update")
    public ResponseResult<Void> update(@MyRequestBody BusinessFileDto businessFileDto) {
        String errorMessage = MyCommonUtil.getModelValidationError(businessFileDto, true);
        if (errorMessage != null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, errorMessage);
        }
        BusinessFile businessFile = MyModelUtil.copyTo(businessFileDto, BusinessFile.class);
        BusinessFile originalBusinessFile = appBusinessFileService.getById(businessFile.getId());
        if (originalBusinessFile == null) {
            // NOTE: 修改下面方括号中的话述
            errorMessage = "数据验证失败，当前 [数据] 并不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        if (!appBusinessFileService.update(businessFile, originalBusinessFile)) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        return ResponseResult.success();
    }

    /**
     * 删除业务附件表数据。
     *
     * @param id 删除对象主键Id。
     * @return 应答结果对象。
     */

    @OperationLog(type = SysOperationLogType.DELETE)
    @PostMapping("/delete")
    public ResponseResult<Void> delete(@MyRequestBody Long id) {
        if (MyCommonUtil.existBlankArgument(id)) {
            return ResponseResult.error(ErrorCodeEnum.ARGUMENT_NULL_EXIST);
        }
        return this.doDelete(id);
    }

    /**
     * 批量删除业务附件表数据。
     *
     * @param idList 待删除对象的主键Id列表。
     * @return 应答结果对象。
     */
    @OperationLog(type = SysOperationLogType.DELETE_BATCH)
    @PostMapping("/deleteBatch")
    public ResponseResult<Void> deleteBatch(@MyRequestBody List<Long> idList) {
        if (MyCommonUtil.existBlankArgument(idList)) {
            return ResponseResult.error(ErrorCodeEnum.ARGUMENT_NULL_EXIST);
        }
        for (Long id : idList) {
            ResponseResult<Void> responseResult = this.doDelete(id);
            if (!responseResult.isSuccess()) {
                return responseResult;
            }
        }
        return ResponseResult.success();
    }

    /**
     * 列出符合过滤条件的业务附件表列表。
     *
     * @param businessFileDtoFilter 过滤对象。
     * @param orderParam 排序参数。
     * @param pageParam 分页参数。
     * @return 应答结果对象，包含查询结果集。
     */

    @PostMapping("/list")
    public ResponseResult<MyPageData<BusinessFileVo>> list(
            @MyRequestBody BusinessFileDto businessFileDtoFilter,
            @MyRequestBody MyOrderParam orderParam,
            @MyRequestBody MyPageParam pageParam) {
        if (pageParam != null) {
            PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize(), pageParam.getCount());
        }
        BusinessFile businessFileFilter = MyModelUtil.copyTo(businessFileDtoFilter, BusinessFile.class);
        String orderBy = MyOrderParam.buildOrderBy(orderParam, BusinessFile.class);
        List<BusinessFile> businessFileList =
                appBusinessFileService.getBusinessFileListWithRelation(businessFileFilter, orderBy);
        return ResponseResult.success(MyPageUtil.makeResponseData(businessFileList, BusinessFileVo.class));
    }

    /**
     * 分组列出符合过滤条件的业务附件表列表。
     *
     * @param businessFileDtoFilter 过滤对象。
     * @param groupParam 分组参数。
     * @param orderParam 排序参数。
     * @param pageParam 分页参数。
     * @return 应答结果对象，包含查询结果集。
     */
    @PostMapping("/listWithGroup")
    public ResponseResult<MyPageData<BusinessFileVo>> listWithGroup(
            @MyRequestBody BusinessFileDto businessFileDtoFilter,
            @MyRequestBody(required = true) MyGroupParam groupParam,
            @MyRequestBody MyOrderParam orderParam,
            @MyRequestBody MyPageParam pageParam) {
        String orderBy = MyOrderParam.buildOrderBy(orderParam, BusinessFile.class, false);
        groupParam = MyGroupParam.buildGroupBy(groupParam, BusinessFile.class);
        if (groupParam == null) {
            return ResponseResult.error(
                    ErrorCodeEnum.INVALID_ARGUMENT_FORMAT, "数据参数错误，分组参数不能为空！");
        }
        if (pageParam != null) {
            PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize(), pageParam.getCount());
        }
        BusinessFile filter = MyModelUtil.copyTo(businessFileDtoFilter, BusinessFile.class);
        MyGroupCriteria criteria = groupParam.getGroupCriteria();
        List<BusinessFile> resultList = appBusinessFileService.getGroupedBusinessFileListWithRelation(
                filter, criteria.getGroupSelect(), criteria.getGroupBy(), orderBy);
        // 分页连同对象数据转换copy工作，下面的方法一并完成。
        return ResponseResult.success(MyPageUtil.makeResponseData(resultList, BusinessFileVo.class));
    }

    /**
     * 查看指定业务附件表对象详情。
     *
     * @param id 指定对象主键Id。
     * @return 应答结果对象，包含对象详情。
     */
    @GetMapping("/view")
    public ResponseResult<BusinessFileVo> view(@RequestParam Long id) {
        BusinessFile businessFile = appBusinessFileService.getByIdWithRelation(id, MyRelationParam.full());
        if (businessFile == null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        BusinessFileVo businessFileVo = MyModelUtil.copyTo(businessFile, BusinessFileVo.class);
        return ResponseResult.success(businessFileVo);
    }

    private ResponseResult<Void> doDelete(Long id) {
        String errorMessage;
        // 验证关联Id的数据合法性
        BusinessFile originalBusinessFile = appBusinessFileService.getById(id);
        if (originalBusinessFile == null) {
            // NOTE: 修改下面方括号中的话述
            errorMessage = "数据验证失败，当前 [对象] 并不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        // appBusinessFileMapper.selfDelete(id)==0
        if (!appBusinessFileService.remove(id)) {
            errorMessage = "数据操作失败，删除的对象不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        return ResponseResult.success();
    }

    /**
     * 文件上传操作。
     *
     * @param fieldName  上传文件名。
     * @param asImage    是否作为图片上传。如果是图片，今后下载的时候无需权限验证。否则就是附件上传，下载时需要权限验证。
     * @param uploadFile 上传文件对象。
     */
    @OperationLog(type = SysOperationLogType.UPLOAD, saveResponse = false)
    @PostMapping("/upload")
    public void upload(
            @RequestParam String fieldName,
            @RequestParam Boolean asImage,
            @RequestParam("uploadFile") MultipartFile uploadFile) throws IOException {
        UploadStoreInfo storeInfo = MyModelUtil.getUploadStoreInfo(BusinessFile.class, fieldName);
        // 这里就会判断参数中指定的字段，是否支持上传操作。
        if (!storeInfo.isSupportUpload()) {
            ResponseResult.output(HttpServletResponse.SC_FORBIDDEN,
                    ResponseResult.error(ErrorCodeEnum.INVALID_UPLOAD_FIELD));
            return;
        }
        // 根据字段注解中的存储类型，通过工厂方法获取匹配的上传下载实现类，从而解耦。
        BaseUpDownloader upDownloader = upDownloaderFactory.get(storeInfo.getStoreType());
        UploadResponseInfo responseInfo = upDownloader.doUpload(null,
                appConfig.getUploadFileBaseDir(), BusinessFile.class.getSimpleName(), fieldName, asImage, uploadFile);
        if (Boolean.TRUE.equals(responseInfo.getUploadFailed())) {
            ResponseResult.output(HttpServletResponse.SC_FORBIDDEN,
                    ResponseResult.error(ErrorCodeEnum.UPLOAD_FAILED, responseInfo.getErrorMessage()));
            return;
        }
        ResponseResult.output(ResponseResult.success(responseInfo));
    }

    /**
     * 附件文件下载。 llm-inferium-dev/image/BusinessFile/fileJson
     * 这里将图片和其他类型的附件文件放到不同的父目录下，主要为了便于今后图片文件的迁移。
     *
     * @param id 附件所在记录的主键Id。
     * @param fieldName 附件所属的字段名。
     * @param filename  文件名。如果没有提供该参数，就从当前记录的指定字段中读取。
     * @param asImage   下载文件是否为图片。
     * @param response  Http 应答对象。
     */
    @DisableDataFilter
    @OperationLog(type = SysOperationLogType.DOWNLOAD, saveResponse = false)
    @GetMapping("/download")
    public void download(
            @RequestParam(required = false) Long id,
            @RequestParam String fieldName,
            @RequestParam String filename,
            @RequestParam Boolean asImage,
            HttpServletResponse response) {
        if (MyCommonUtil.existBlankArgument(fieldName, filename, asImage)) {
            response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
            return;
        }
        // 使用try来捕获异常，是为了保证一旦出现异常可以返回500的错误状态，便于调试。
        try {
            // 如果请求参数中没有包含主键Id，就判断该文件是否为当前session上传的。
            if (id == null) {
                if (!cacheHelper.existSessionUploadFile(filename)) {
                    ResponseResult.output(HttpServletResponse.SC_FORBIDDEN);
                    return;
                }
            } else {
                BusinessFile businessFile = appBusinessFileService.getById(id);
                if (businessFile == null) {
                    ResponseResult.output(HttpServletResponse.SC_NOT_FOUND);
                    return;
                }
                String fieldJsonData = (String) ReflectUtil.getFieldValue(businessFile, fieldName);
                if (fieldJsonData == null && !cacheHelper.existSessionUploadFile(filename)) {
                    ResponseResult.output(HttpServletResponse.SC_BAD_REQUEST);
                    return;
                }
                if (!BaseUpDownloader.containFile(fieldJsonData, filename)
                        && !cacheHelper.existSessionUploadFile(filename)) {
                    ResponseResult.output(HttpServletResponse.SC_FORBIDDEN);
                    return;
                }
            }
            UploadStoreInfo storeInfo = MyModelUtil.getUploadStoreInfo(BusinessFile.class, fieldName);
            if (!storeInfo.isSupportUpload()) {
                ResponseResult.output(HttpServletResponse.SC_NOT_IMPLEMENTED,
                        ResponseResult.error(ErrorCodeEnum.INVALID_UPLOAD_FIELD));
                return;
            }
            BaseUpDownloader upDownloader = upDownloaderFactory.get(storeInfo.getStoreType());
            upDownloader.doDownload(appConfig.getUploadFileBaseDir(),
                    BusinessFile.class.getSimpleName(), fieldName, filename, asImage, response);
        } catch (Exception e) {
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            log.error(e.getMessage(), e);
        }
    }

    /**
     * 查询文件路径（在Minio中的路径）
     */
    @Operation(summary = "查询文件路径")
    @GetMapping("/getFilePath")
    public ResponseResult<String> getFilePath(@RequestParam Long fileId) {
        if (fileId == null) {
            return ResponseResult.error(ErrorCodeEnum.ARGUMENT_PK_ID_NULL);
        }
        BusinessFile businessFile = appBusinessFileService.getById(fileId);
        if (businessFile == null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        JSONObject fileJson = JSON.parseObject(businessFile.getFileJson());
        String filename = fileJson.getString("filename");
        Boolean asImage = fileJson.getBoolean("asImage");
        return ResponseResult.success(Boolean.TRUE.equals(asImage) ? "/image/BusinessFile/fileJson/" + filename : "/attachment/BusinessFile/fileJson/" + filename);
    }

}
