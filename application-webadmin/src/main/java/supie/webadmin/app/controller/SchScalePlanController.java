package supie.webadmin.app.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import supie.common.log.annotation.OperationLog;
import supie.common.log.model.constant.SysOperationLogType;
import com.github.pagehelper.page.PageMethod;
import supie.webadmin.app.vo.*;
import supie.webadmin.app.dto.*;
import supie.webadmin.app.model.*;
import supie.webadmin.app.service.*;
import supie.common.core.object.*;
import supie.common.core.util.*;
import supie.common.core.constant.*;
import supie.common.core.annotation.MyRequestBody;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * 扩缩容执行计划表操作控制器类。
 *
 * <AUTHOR> -rf .bug
 * @date 2025-05-16
 */
@Tag(name = "扩缩容执行计划表管理接口")
@Slf4j
@RestController
@RequestMapping("/admin/app/schScalePlan")
public class SchScalePlanController {

    @Autowired
    private SchScalePlanService schScalePlanService;

    /**
     * 新增扩缩容执行计划表数据。
     *
     * @param schScalePlanDto 新增对象。
     * @return 应答结果对象，包含新增对象主键Id。
     */
    @ApiOperationSupport(ignoreParameters = {
            "schScalePlanDto.id",
            "schScalePlanDto.searchString",
            "schScalePlanDto.updateTimeStart",
            "schScalePlanDto.updateTimeEnd",
            "schScalePlanDto.createTimeStart",
            "schScalePlanDto.createTimeEnd"})
    @SaCheckPermission("schScalePlan.add")
    @OperationLog(type = SysOperationLogType.ADD)
    @PostMapping("/add")
    public ResponseResult<Long> add(@MyRequestBody SchScalePlanDto schScalePlanDto) {
        String errorMessage = MyCommonUtil.getModelValidationError(schScalePlanDto, false);
        if (errorMessage != null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, errorMessage);
        }
        SchScalePlan schScalePlan = MyModelUtil.copyTo(schScalePlanDto, SchScalePlan.class);
        schScalePlan = schScalePlanService.saveNew(schScalePlan);
        return ResponseResult.success(schScalePlan.getId());
    }

    /**
     * 更新扩缩容执行计划表数据。
     *
     * @param schScalePlanDto 更新对象。
     * @return 应答结果对象。
     */
    @ApiOperationSupport(ignoreParameters = {
            "schScalePlanDto.searchString",
            "schScalePlanDto.updateTimeStart",
            "schScalePlanDto.updateTimeEnd",
            "schScalePlanDto.createTimeStart",
            "schScalePlanDto.createTimeEnd"})
    @SaCheckPermission("schScalePlan.update")
    @OperationLog(type = SysOperationLogType.UPDATE)
    @PostMapping("/update")
    public ResponseResult<Void> update(@MyRequestBody SchScalePlanDto schScalePlanDto) {
        String errorMessage = MyCommonUtil.getModelValidationError(schScalePlanDto, true);
        if (errorMessage != null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, errorMessage);
        }
        SchScalePlan schScalePlan = MyModelUtil.copyTo(schScalePlanDto, SchScalePlan.class);
        SchScalePlan originalSchScalePlan = schScalePlanService.getById(schScalePlan.getId());
        if (originalSchScalePlan == null) {
            // NOTE: 修改下面方括号中的话述
            errorMessage = "数据验证失败，当前 [数据] 并不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        if (!schScalePlanService.update(schScalePlan, originalSchScalePlan)) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        return ResponseResult.success();
    }

    /**
     * 删除扩缩容执行计划表数据。
     *
     * @param id 删除对象主键Id。
     * @return 应答结果对象。
     */
    @SaCheckPermission("schScalePlan.delete")
    @OperationLog(type = SysOperationLogType.DELETE)
    @PostMapping("/delete")
    public ResponseResult<Void> delete(@MyRequestBody Long id) {
        if (MyCommonUtil.existBlankArgument(id)) {
            return ResponseResult.error(ErrorCodeEnum.ARGUMENT_NULL_EXIST);
        }
        return this.doDelete(id);
    }

    /**
     * 批量删除扩缩容执行计划表数据。
     *
     * @param idList 待删除对象的主键Id列表。
     * @return 应答结果对象。
     */
    @SaCheckPermission("schScalePlan.delete")
    @OperationLog(type = SysOperationLogType.DELETE_BATCH)
    @PostMapping("/deleteBatch")
    public ResponseResult<Void> deleteBatch(@MyRequestBody List<Long> idList) {
        if (MyCommonUtil.existBlankArgument(idList)) {
            return ResponseResult.error(ErrorCodeEnum.ARGUMENT_NULL_EXIST);
        }
        for (Long id : idList) {
            ResponseResult<Void> responseResult = this.doDelete(id);
            if (!responseResult.isSuccess()) {
                return responseResult;
            }
        }
        return ResponseResult.success();
    }

    /**
     * 列出符合过滤条件的扩缩容执行计划表列表。
     *
     * @param schScalePlanDtoFilter 过滤对象。
     * @param orderParam 排序参数。
     * @param pageParam 分页参数。
     * @return 应答结果对象，包含查询结果集。
     */
    @SaCheckPermission("schScalePlan.view")
    @PostMapping("/list")
    public ResponseResult<MyPageData<SchScalePlanVo>> list(
            @MyRequestBody SchScalePlanDto schScalePlanDtoFilter,
            @MyRequestBody MyOrderParam orderParam,
            @MyRequestBody MyPageParam pageParam) {
        if (pageParam != null) {
            PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize(), pageParam.getCount());
        }
        SchScalePlan schScalePlanFilter = MyModelUtil.copyTo(schScalePlanDtoFilter, SchScalePlan.class);
        String orderBy = MyOrderParam.buildOrderBy(orderParam, SchScalePlan.class);
        List<SchScalePlan> schScalePlanList =
                schScalePlanService.getSchScalePlanListWithRelation(schScalePlanFilter, orderBy);
        return ResponseResult.success(MyPageUtil.makeResponseData(schScalePlanList, SchScalePlanVo.class));
    }

    /**
     * 分组列出符合过滤条件的扩缩容执行计划表列表。
     *
     * @param schScalePlanDtoFilter 过滤对象。
     * @param groupParam 分组参数。
     * @param orderParam 排序参数。
     * @param pageParam 分页参数。
     * @return 应答结果对象，包含查询结果集。
     */
    @SaCheckPermission("schScalePlan.view")
    @PostMapping("/listWithGroup")
    public ResponseResult<MyPageData<SchScalePlanVo>> listWithGroup(
            @MyRequestBody SchScalePlanDto schScalePlanDtoFilter,
            @MyRequestBody(required = true) MyGroupParam groupParam,
            @MyRequestBody MyOrderParam orderParam,
            @MyRequestBody MyPageParam pageParam) {
        String orderBy = MyOrderParam.buildOrderBy(orderParam, SchScalePlan.class, false);
        groupParam = MyGroupParam.buildGroupBy(groupParam, SchScalePlan.class);
        if (groupParam == null) {
            return ResponseResult.error(
                    ErrorCodeEnum.INVALID_ARGUMENT_FORMAT, "数据参数错误，分组参数不能为空！");
        }
        if (pageParam != null) {
            PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize(), pageParam.getCount());
        }
        SchScalePlan filter = MyModelUtil.copyTo(schScalePlanDtoFilter, SchScalePlan.class);
        MyGroupCriteria criteria = groupParam.getGroupCriteria();
        List<SchScalePlan> resultList = schScalePlanService.getGroupedSchScalePlanListWithRelation(
                filter, criteria.getGroupSelect(), criteria.getGroupBy(), orderBy);
        // 分页连同对象数据转换copy工作，下面的方法一并完成。
        return ResponseResult.success(MyPageUtil.makeResponseData(resultList, SchScalePlanVo.class));
    }

    /**
     * 查看指定扩缩容执行计划表对象详情。
     *
     * @param id 指定对象主键Id。
     * @return 应答结果对象，包含对象详情。
     */
    @SaCheckPermission("schScalePlan.view")
    @GetMapping("/view")
    public ResponseResult<SchScalePlanVo> view(@RequestParam Long id) {
        SchScalePlan schScalePlan = schScalePlanService.getByIdWithRelation(id, MyRelationParam.full());
        if (schScalePlan == null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        SchScalePlanVo schScalePlanVo = MyModelUtil.copyTo(schScalePlan, SchScalePlanVo.class);
        return ResponseResult.success(schScalePlanVo);
    }

    private ResponseResult<Void> doDelete(Long id) {
        String errorMessage;
        // 验证关联Id的数据合法性
        SchScalePlan originalSchScalePlan = schScalePlanService.getById(id);
        if (originalSchScalePlan == null) {
            // NOTE: 修改下面方括号中的话述
            errorMessage = "数据验证失败，当前 [对象] 并不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        if (!schScalePlanService.remove(id)) {
            errorMessage = "数据操作失败，删除的对象不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        return ResponseResult.success();
    }
}
