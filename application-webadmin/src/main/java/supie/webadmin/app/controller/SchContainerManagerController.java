package supie.webadmin.app.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaIgnore;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import jakarta.annotation.Resource;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyEmitter;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;
import supie.common.core.exception.MyRuntimeException;
import supie.common.log.annotation.OperationLog;
import supie.common.log.model.constant.SysOperationLogType;
import com.github.pagehelper.page.PageMethod;
import supie.webadmin.app.dao.SchContainerManagerMapper;
import supie.webadmin.app.dao.SchResourceInfoMapper;
import supie.webadmin.app.dao.SchTaskInfoMapper;
import supie.webadmin.app.dao.SchTaskMonitoringMapper;
import supie.webadmin.app.service.impl.SchClusterNodeServiceImpl;
import supie.webadmin.app.util.DockerJavaUtil;
import supie.webadmin.app.util.DockerLogUtil;
import supie.webadmin.app.vo.*;
import supie.webadmin.app.dto.*;
import supie.webadmin.app.model.*;
import supie.webadmin.app.service.*;
import supie.common.core.object.*;
import supie.common.core.util.*;
import supie.common.core.constant.*;
import supie.common.core.annotation.MyRequestBody;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * 容器管理操作控制器类。
 *
 * <AUTHOR> -rf .bug
 * @date 2025-05-16
 */
@Tag(name = "k8s容器管理管理接口")
@Slf4j
@RestController
@RequestMapping("/admin/app/schContainerManager")
public class SchContainerManagerController {

    @Autowired
    private SchContainerManagerService schContainerManagerService;
    @Autowired
    private DockerJavaUtil dockerJavaUtil;
    @Autowired
    private SchClusterNodeServiceImpl schClusterNodeService;

    @Resource
    private SchResourceInfoMapper schResourceInfoMapper;

    @Resource
    private DockerLogUtil dockerLogUtil;

    @Resource
    private SchContainerManagerMapper schContainerManagerMapper;
    
    @Resource
    private SchTaskInfoMapper schTaskInfoMapper;

    /**
     * 新增容器管理数据。
     *
     * @param schContainerManagerDto 新增对象。
     * @return 应答结果对象，包含新增对象主键Id。
     */
    @ApiOperationSupport(ignoreParameters = {
            "schContainerManagerDto.id",
            "schContainerManagerDto.searchString",
            "schContainerManagerDto.containerCreateTimeStart",
            "schContainerManagerDto.containerCreateTimeEnd",
            "schContainerManagerDto.containerOperationTimeStart",
            "schContainerManagerDto.containerOperationTimeEnd",
            "schContainerManagerDto.updateTimeStart",
            "schContainerManagerDto.updateTimeEnd",
            "schContainerManagerDto.createTimeStart",
            "schContainerManagerDto.createTimeEnd"})
    @SaCheckPermission("schContainerManager.add")
    @OperationLog(type = SysOperationLogType.ADD)
    @PostMapping("/add")
    public ResponseResult<Long> add(@MyRequestBody SchContainerManagerDto schContainerManagerDto) {
        String errorMessage = MyCommonUtil.getModelValidationError(schContainerManagerDto, false);
        if (errorMessage != null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, errorMessage);
        }
        SchContainerManager schContainerManager = MyModelUtil.copyTo(schContainerManagerDto, SchContainerManager.class);
        schContainerManager = schContainerManagerService.saveNew(schContainerManager);
        return ResponseResult.success(schContainerManager.getId());
    }

    /**
     * 更新容器管理数据。
     *
     * @param schContainerManagerDto 更新对象。
     * @return 应答结果对象。
     */
    @ApiOperationSupport(ignoreParameters = {
            "schContainerManagerDto.searchString",
            "schContainerManagerDto.containerCreateTimeStart",
            "schContainerManagerDto.containerCreateTimeEnd",
            "schContainerManagerDto.containerOperationTimeStart",
            "schContainerManagerDto.containerOperationTimeEnd",
            "schContainerManagerDto.updateTimeStart",
            "schContainerManagerDto.updateTimeEnd",
            "schContainerManagerDto.createTimeStart",
            "schContainerManagerDto.createTimeEnd"})
    @SaCheckPermission("schContainerManager.update")
    @OperationLog(type = SysOperationLogType.UPDATE)
    @PostMapping("/update")
    public ResponseResult<Void> update(@MyRequestBody SchContainerManagerDto schContainerManagerDto) {
        String errorMessage = MyCommonUtil.getModelValidationError(schContainerManagerDto, true);
        if (errorMessage != null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, errorMessage);
        }
        SchContainerManager schContainerManager = MyModelUtil.copyTo(schContainerManagerDto, SchContainerManager.class);
        SchContainerManager originalSchContainerManager = schContainerManagerService.getById(schContainerManager.getId());
        if (originalSchContainerManager == null) {
            // NOTE: 修改下面方括号中的话述
            errorMessage = "数据验证失败，当前 [数据] 并不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        if (!schContainerManagerService.update(schContainerManager, originalSchContainerManager)) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        return ResponseResult.success();
    }

    /**
     * 删除容器管理数据。
     *
     * @param id 删除对象主键Id。
     * @return 应答结果对象。
     */
    @SaCheckPermission("schContainerManager.delete")
    @OperationLog(type = SysOperationLogType.DELETE)
    @PostMapping("/delete")
    public ResponseResult<Void> delete(@MyRequestBody Long id) {
        if (MyCommonUtil.existBlankArgument(id)) {
            return ResponseResult.error(ErrorCodeEnum.ARGUMENT_NULL_EXIST);
        }
        return this.doDelete(id);
    }

    /**
     * 批量删除容器管理数据。
     *
     * @param idList 待删除对象的主键Id列表。
     * @return 应答结果对象。
     */
    @SaCheckPermission("schContainerManager.delete")
    @OperationLog(type = SysOperationLogType.DELETE_BATCH)
    @PostMapping("/deleteBatch")
    public ResponseResult<Void> deleteBatch(@MyRequestBody List<Long> idList) {
        if (MyCommonUtil.existBlankArgument(idList)) {
            return ResponseResult.error(ErrorCodeEnum.ARGUMENT_NULL_EXIST);
        }
        for (Long id : idList) {
            ResponseResult<Void> responseResult = this.doDelete(id);
            if (!responseResult.isSuccess()) {
                return responseResult;
            }
        }
        return ResponseResult.success();
    }

    /**
     * 列出符合过滤条件的容器管理列表。
     *
     * @param schContainerManagerDtoFilter 过滤对象。
     * @param orderParam 排序参数。
     * @param pageParam 分页参数。
     * @return 应答结果对象，包含查询结果集。
     */
    @SaCheckPermission("schContainerManager.view")
    @PostMapping("/list")
    public ResponseResult<MyPageData<SchContainerManagerVo>> list(
            @MyRequestBody SchContainerManagerDto schContainerManagerDtoFilter,
            @MyRequestBody MyOrderParam orderParam,
            @MyRequestBody MyPageParam pageParam) {
        if (pageParam != null) {
            PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize(), pageParam.getCount());
        }
        SchContainerManager schContainerManagerFilter = MyModelUtil.copyTo(schContainerManagerDtoFilter, SchContainerManager.class);
        String orderBy = MyOrderParam.buildOrderBy(orderParam, SchContainerManager.class);
        List<SchContainerManager> schContainerManagerList =
                schContainerManagerService.getSchContainerManagerListWithRelation(schContainerManagerFilter, orderBy);
        return ResponseResult.success(MyPageUtil.makeResponseData(schContainerManagerList, SchContainerManagerVo.class));
    }

    /**
     * 分组列出符合过滤条件的容器管理列表。
     *
     * @param schContainerManagerDtoFilter 过滤对象。
     * @param groupParam 分组参数。
     * @param orderParam 排序参数。
     * @param pageParam 分页参数。
     * @return 应答结果对象，包含查询结果集。
     */
    @SaCheckPermission("schContainerManager.view")
    @PostMapping("/listWithGroup")
    public ResponseResult<MyPageData<SchContainerManagerVo>> listWithGroup(
            @MyRequestBody SchContainerManagerDto schContainerManagerDtoFilter,
            @MyRequestBody(required = true) MyGroupParam groupParam,
            @MyRequestBody MyOrderParam orderParam,
            @MyRequestBody MyPageParam pageParam) {
        String orderBy = MyOrderParam.buildOrderBy(orderParam, SchContainerManager.class, false);
        groupParam = MyGroupParam.buildGroupBy(groupParam, SchContainerManager.class);
        if (groupParam == null) {
            return ResponseResult.error(
                    ErrorCodeEnum.INVALID_ARGUMENT_FORMAT, "数据参数错误，分组参数不能为空！");
        }
        if (pageParam != null) {
            PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize(), pageParam.getCount());
        }
        SchContainerManager filter = MyModelUtil.copyTo(schContainerManagerDtoFilter, SchContainerManager.class);
        MyGroupCriteria criteria = groupParam.getGroupCriteria();
        List<SchContainerManager> resultList = schContainerManagerService.getGroupedSchContainerManagerListWithRelation(
                filter, criteria.getGroupSelect(), criteria.getGroupBy(), orderBy);
        // 分页连同对象数据转换copy工作，下面的方法一并完成。
        return ResponseResult.success(MyPageUtil.makeResponseData(resultList, SchContainerManagerVo.class));
    }

    /**
     * 查看指定容器管理对象详情。
     *
     * @param id 指定对象主键Id。
     * @return 应答结果对象，包含对象详情。
     */
    @SaCheckPermission("schContainerManager.view")
    @GetMapping("/view")
    public ResponseResult<SchContainerManagerVo> view(@RequestParam Long id) {
        SchContainerManager schContainerManager = schContainerManagerService.getByIdWithRelation(id, MyRelationParam.full());
        if (schContainerManager == null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        SchContainerManagerVo schContainerManagerVo = MyModelUtil.copyTo(schContainerManager, SchContainerManagerVo.class);
        return ResponseResult.success(schContainerManagerVo);
    }

    private ResponseResult<Void> doDelete(Long id) {
        String errorMessage;
        // 验证关联Id的数据合法性
        SchContainerManager originalSchContainerManager = schContainerManagerService.getById(id);
        if (originalSchContainerManager == null) {
            // NOTE: 修改下面方括号中的话述
            errorMessage = "数据验证失败，当前 [对象] 并不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        if (!schContainerManagerService.remove(id)) {
            errorMessage = "数据操作失败，删除的对象不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
        }
        return ResponseResult.success();
    }

    /**
     * 指定容器操作。
     *
     * @param schContainerManagerId 指定对象主键Id。
     * @param operation 容器操作。
     * @return 应答结果对象，包含对象详情。
     */
    @PostMapping("/containerOperation")
    public ResponseResult<Void> containerOperation(@MyRequestBody Long schContainerManagerId,@MyRequestBody String operation) {
        String errorMessage;
        if(operation==null || schContainerManagerId==null){
            return ResponseResult.error(ErrorCodeEnum.ARGUMENT_NULL_EXIST);
        }
        // 验证关联Id的数据合法性
        SchContainerManager originalSchContainerManager = schContainerManagerService.getById(schContainerManagerId);
        if (originalSchContainerManager == null) {
            // NOTE: 修改下面方括号中的话述
            errorMessage = "数据验证失败，当前 [对象] 并不存在，请刷新后重试！";
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST, errorMessage);
       }
        schContainerManagerService.changeServiceStatus(originalSchContainerManager, operation);
        return ResponseResult.success();
    }


    /**
     * 获取容器日志（非流式）
     *
     * @param id 容器管理ID
     * @param lines 日志行数，默认100行
     * @return 容器日志内容
     */
    @Parameters(
            value = {
                    @Parameter(name = "id", description = "任务表主键ID", required = true, in = ParameterIn.QUERY),
                    @Parameter(name = "lines", description = "日志行数，默认100行", required = false, in = ParameterIn.QUERY)
            }
    )
    @SaIgnore
    @GetMapping("/containerLogsText")
    public ResponseResult<String> containerLogsText(
            @RequestParam String id,
            @RequestParam(required = false, defaultValue = "100") int lines) {

        SchTaskInfo schTaskInfo = schTaskInfoMapper.selectById(Long.valueOf(id));
        if(schTaskInfo.getContainerName()==null || schTaskInfo.getContainerStatus()==null){
            return ResponseResult.success("任务启动失败--"+schTaskInfo.getFailReason());
        }
        SchContainerManager containerManager = schContainerManagerMapper.queryContainerMsg(Long.valueOf(id));
        SchResourceInfo  resource = schResourceInfoMapper.queryResource(containerManager.getTaskInfoId());
        String containerId = containerManager.getContainerId();
        String logs = dockerJavaUtil.getContainerLogs(containerId, lines, resource.getId());
        return ResponseResult.success(logs);
    }

    /**
     * 获取容器日志（非流式）
     *
     * @param id 容器管理ID
     * @param lines 日志行数，默认100行
     * @return 容器日志内容
     */
    @Parameters(
            value = {
                    @Parameter(name = "id", description = "task表主键ID", required = true, in = ParameterIn.QUERY),
                    @Parameter(name = "lines", description = "日志行数，默认100行", required = false, in = ParameterIn.QUERY)
            }
    )

   // @GetMapping("/dockerLogs")
    @SaIgnore
    public ResponseResult<String> containerLogs(
            @RequestParam String id,
            @RequestParam(required = false, defaultValue = "100") int lines) {
        SchContainerManager containerManager = schContainerManagerMapper.queryContainerMsg(Long.valueOf(id));
        if (containerManager == null) {
            return ResponseResult.error("500","数据不存在，请核对后重试！");
        }
        SchResourceInfo  resource = schResourceInfoMapper.queryResource(containerManager.getTaskInfoId());
        String containerId = containerManager.getContainerId();
        String dockerLogs = dockerLogUtil.getDockerLogs(resource.getHostIp(), resource.getPort(), resource.getLoginName(),
                resource.getPassword(), containerId, lines, 300000);
        return ResponseResult.success(dockerLogs);
    }

    /**
     * 获取容器日志（流式）
     *
     * @param id 容器管理ID
     * @param lines 日志行数，默认100行，-1表示全部
     * @param since 开始时间戳（秒）
     * @param until 结束时间戳（秒）
     * @param follow 是否持续跟踪日志
     * @return SSE事件流
     */
    @Parameters({
            @Parameter(name = "id", description = "容器ID", required = true, in = ParameterIn.QUERY),
            @Parameter(name = "lines", description = "日志行数，默认100行，-1表示全部", required = false, in = ParameterIn.QUERY),
            @Parameter(name = "since", description = "开始时间戳（秒）", required = false, in = ParameterIn.QUERY),
            @Parameter(name = "until", description = "结束时间戳（秒）", required = false, in = ParameterIn.QUERY),
            @Parameter(name = "follow", description = "是否持续跟踪日志", required = false, in = ParameterIn.QUERY)
    })
    @SaIgnore
    @GetMapping("/containerLogs")
    public SseEmitter containerLogs(
            @RequestParam String id,
            @RequestParam(required = false, defaultValue = "100") int lines,
            @RequestParam(required = false) Long since,
            @RequestParam(required = false) Long until,
            @RequestParam(required = false, defaultValue = "true") boolean follow) {
        
        SchContainerManager containerManager = schContainerManagerService.getById(id);
        if (containerManager == null) {
            throw new RuntimeException("数据不存在，请核对后重试！");
        }
        
        String containerId = containerManager.getContainerId();
        SchClusterNode schClusterNode = schClusterNodeService.getById(containerManager.getClusterNodeId());
        if (schClusterNode == null) {
            throw new RuntimeException("集群节点数据不存在，请核对后重试！");
        }
        
        Long schResourceInfoId = schClusterNode.getResourceInfoId();

        long timeout = follow ? 3600_000L : 60_000L;
        SseEmitter emitter = new SseEmitter(timeout);
        
        new Thread(() -> {
            try {
                dockerJavaUtil.streamContainerLogs(containerId, schResourceInfoId, emitter, lines, since, until, follow);
            } catch (Exception e) {
                emitter.completeWithError(e);
            }
        }).start();
        
        return emitter;
    }

    /**
     * 容器状态统计。
     *
     * @return 应答结果对象，包含对象详情。
     */
    @GetMapping("/containerStateStatistics")
    public ResponseResult<Map<String, Object>> containerStateStatistics() {
        return ResponseResult.success(schContainerManagerService.containerStateStatistics());
    }

    /**
     * 创建并启动容器。
     *
     * @return 应答结果对象，包含对象详情。
     */
    @PostMapping("/createAndStartContainer")
    public ResponseResult<SchContainerManagerVo> createAndStartContainer(@MyRequestBody SchContainerManagerDto schContainerManagerDto) {
        String errorMessage = MyCommonUtil.getModelValidationError(schContainerManagerDto, false);
        if (errorMessage != null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, errorMessage);
        }
        SchContainerManager schContainerManager = MyModelUtil.copyTo(schContainerManagerDto, SchContainerManager.class);
        schContainerManager = schContainerManagerService.createAndStartContainer(schContainerManager);
        SchContainerManagerVo schContainerManagerVo = MyModelUtil.copyTo(schContainerManager, SchContainerManagerVo.class);
        return ResponseResult.success(schContainerManagerVo);
    }

    /**
     * 删除容器。
     *
     * @return 应答结果对象，包含对象详情。
     */
    @PostMapping("/deleteContainer")
    public ResponseResult<Void> deleteContainer(@MyRequestBody Long id) {
        SchContainerManager schContainerManager = schContainerManagerService.getByIdWithRelation(id, MyRelationParam.full());
        if (schContainerManager == null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        schContainerManagerService.deleteContainer(schContainerManager);
        return ResponseResult.success();
    }
}
