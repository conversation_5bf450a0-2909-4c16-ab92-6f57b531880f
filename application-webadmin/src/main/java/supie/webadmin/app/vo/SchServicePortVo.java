package supie.webadmin.app.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * K8S Service端口配置表VO视图对象。
 *
 * <AUTHOR> -rf .bug
 * @date 2025-05-16
 */
@Schema(description = "K8S Service端口配置表VO视图对象")
@Data
public class SchServicePortVo {

    /**
     * 端口配置主键id。
     */
    @Schema(description = "端口配置主键id")
    private Long id;

    /**
     * 服务ID。
     */
    @Schema(description = "服务ID")
    private Long serviceId;

    /**
     * 服务端口。
     */
    @Schema(description = "服务端口")
    private Integer port;

    /**
     * 目标端口。
     */
    @Schema(description = "目标端口")
    private String targetPort;

    /**
     * 节点端口(NodePort类型时使用)。
     */
    @Schema(description = "节点端口(NodePort类型时使用)")
    private Integer nodePort;

    /**
     * 协议(TCP/UDP/SCTP)。
     */
    @Schema(description = "协议(TCP/UDP/SCTP)")
    private String protocol;

    /**
     * 端口名称。
     */
    @Schema(description = "端口名称")
    private String name;

    /**
     * 字符id。
     */
    @Schema(description = "字符id")
    private String strId;

    /**
     * 更新时间。
     */
    @Schema(description = "更新时间")
    private Date updateTime;

    /**
     * 创建时间。
     */
    @Schema(description = "创建时间")
    private Date createTime;

    /**
     * 创建人。
     */
    @Schema(description = "创建人")
    private Long createUserId;

    /**
     * 更新人。
     */
    @Schema(description = "更新人")
    private Long updateUserId;

    /**
     * 数据所属人。
     */
    @Schema(description = "数据所属人")
    private Long dataUserId;

    /**
     * 数据所属部门。
     */
    @Schema(description = "数据所属部门")
    private Long dataDeptId;
}
