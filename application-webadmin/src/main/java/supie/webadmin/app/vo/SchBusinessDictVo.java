package supie.webadmin.app.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import supie.webadmin.app.model.SchTaskInfo;

import java.util.Date;
import java.util.List;

/**
 * 业务字典表VO视图对象。
 *
 * <AUTHOR> -rf .bug
 * @date 2025-05-16
 */
@Schema(description = "业务字典表VO视图对象")
@Data
public class SchBusinessDictVo {

    /**
     * 编号。
     */
    @Schema(description = "编号")
    private Long id;

    /**
     * 字符编号。
     */
    @Schema(description = "字符编号")
    private String strId;

    /**
     * 更新时间。
     */
    @Schema(description = "更新时间")
    private Date updateTime;

    /**
     * 创建时间。
     */
    @Schema(description = "创建时间")
    private Date createTime;

    /**
     * 创建人。
     */
    @Schema(description = "创建人")
    private Long createUserId;

    /**
     * 更新人。
     */
    @Schema(description = "更新人")
    private Long updateUserId;

    /**
     * 数据所属人。
     */
    @Schema(description = "数据所属人")
    private Long dataUserId;

    /**
     * 数据所属部门。
     */
    @Schema(description = "数据所属部门")
    private Long dataDeptId;

    /**
     * 显示顺序。
     */
    @Schema(description = "显示顺序")
    private Integer showOrder;

    /**
     * 父级编号。
     */
    @Schema(description = "父级编号")
    private Long parentId;

    /**
     * 绑定类型。
     */
    @Schema(description = "绑定类型")
    private String bindType;

    /**
     * 名称。
     */
    @Schema(description = "名称")
    private String dictName;

    /**
     * 描述。
     */
    @Schema(description = "描述")
    private String dictDescription;

    /**
     * 颜色数据。
     */
    @Schema(description = "颜色数据")
    private String colorData;

    /**
     * 其他数据。
     */
    @Schema(description = "其他数据")
    private String otherData;

    /**
     * 层级。
     */
    @Schema(description = "层级")
    private Integer dictLevel;

    /**
     * 任务信息。
     */
    @TableField(exist = false)
    private List<SchTaskInfo> schTaskInfoList;
}
