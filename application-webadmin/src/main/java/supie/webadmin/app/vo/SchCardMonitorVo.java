package supie.webadmin.app.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;

/**
 * 服务卡监控(NPU,GPU监控)VO视图对象。
 *
 * <AUTHOR> -rf .bug
 * @date 2025-05-16
 */
@Schema(description = "服务卡监控(NPU,GPU监控)VO视图对象")
@Data
public class SchCardMonitorVo {

    /**
     * 主键id。
     */
    @Schema(description = "主键id")
    private Long id;

    /**
     * 资源表主键id。
     */
    @Schema(description = "资源表主键id")
    private Long resourceId;

    /**
     * 计算卡表id。
     */
    @Schema(description = "计算卡表id")
    private Long computeDeviceId;

    /**
     * 卡利用率。
     */
    @Schema(description = "卡利用率")
    private BigDecimal cardUtilization;

    /**
     * 监控类型(1NPU,2GPU)。
     */
    @Schema(description = "监控类型(1NPU,2GPU)")
    private Integer monitorType;

    /**
     * 更新时间。
     */
    @Schema(description = "更新时间")
    private Date updateTime;

    /*
     * 卡高带宽内存总容量（HBM）
     * */
    @Schema(description = "卡高带宽内存总容量")
    private Long hbTotal;

    /*
     * 卡高带宽内存（HBM）利用率
     * */
    @Schema(description = "卡高带宽内存（HBM）利用率")
    private String hbUtil;

    /*
     * 卡高带宽内存已使用总容量（HBM）
     * */
    @Schema(description = "卡高带宽内存已使用总容量（HBM）")
    private Long hbUsed;


    /*
     * 通用内存总容量
     * */
    @Schema(description = "通用内存总容量")
    private Long memoryTotal;

    /*
     * 已使用通用内存总容量
     * */
    @Schema(description = "已使用通用内存总容量")
    private Long memoryUsed;

    /*
     * 通用内存利用率
     * */
    @Schema(description = "通用内存利用率")
    private BigDecimal memoryUtil;

    /**
     * 序号
     */
    @Schema(description = "序号")
    private Integer serialNumber;

    /**
     * 创建时间。
     */
    @Schema(description = "创建时间")
    private Date createTime;

    /**
     * 创建人。
     */
    @Schema(description = "创建人")
    private Long createUserId;

    /**
     * 更新人。
     */
    @Schema(description = "更新人")
    private Long updateUserId;

    /**
     * 数据所属人。
     */
    @Schema(description = "数据所属人")
    private Long dataUserId;

    /**
     * 数据所属部门。
     */
    @Schema(description = "数据所属部门")
    private Long dataDeptId;

    /**
     * 字符串ID。
     */
    @Schema(description = "字符串ID")
    private String strId;

    /**
     * ts 范围过滤起始值(>=)。
     */
    @Schema(description = "ts")
    private Timestamp ts;

    /**
     * 温度（以摄氏度为单位）
     */
    private Integer temp;

    /**
     * 总内存信息（单位：MB）
     */
    private Long totalMemMemory;

    /**
     * 已使用内存（单位：MB）
     */
    private Long usedMemMemory;

    /**
     * 空闲显存
     */
    private Long fbFree;

    /**
     * 总显存
     */
    private Long fbTotal;

    /**
     * 已使用显存
     */
    private Long fbUsed;
}
