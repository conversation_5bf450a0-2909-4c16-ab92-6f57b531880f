package supie.webadmin.app.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import supie.common.core.annotation.RelationOneToOne;
import supie.webadmin.app.model.*;

import java.util.Date;

/**
 * 任务表VO视图对象。
 *
 * <AUTHOR> -rf .bug
 * @date 2025-05-16
 */
@Schema(description = "任务表VO视图对象")
@Data
public class SchTaskInfoVo {

    /**
     * 容器退出码(exited(0)容器正常退出 exited(非零) 容器异常 )
     */
    @Schema(description = "容器退出码(exited(0)容器正常退出 exited(非零) 容器异常 )")
    private  String exitCode;

    /**
     * 任务失败原因
     */
    @Schema(description = "任务失败原因描述")
    private String failReason;

    /**
     * 字典表主键id
     */
    @Schema(description = "字典表主键id")
    private Long dictId;
    /**
     * 容器状态 容器健康状态(running运行中、 paused暂停、restarting重启中、 exited 异常退出 / exited (0)正常退出、dead死亡）
     */
    @Schema(description = "容器状态 容器健康状态(running运行中、 paused暂停、restarting重启中、 exited 异常退出 / exited (0)正常退出、dead死亡）")
    private String containerStatus;

    /**
     * 审批信息记录
     */
    @Schema(description = "审批信息记录")
    private SchTaskApproval schTaskApproval;
    /**
     * 构建资源池信息
     */
    @Schema(description = "构建资源池信息")
    private SchResourcePool schResourcePool;

    /**
     * 服务资源信息
     */
    @Schema(description = "服务资源信息")
    private SchResourceInfo schResourceInfo;


    /**
     * 显卡信息
     */
    @Schema(description = "显卡信息")
    private SchComputeDevice schComputeDevice;

    /**
     * 卡切分信息
     */
    @Schema(description = "卡切分信息")
    private SchVirtualComputeCardSituation partition;

    /**
     * 镜像表关联数据
     */
    @Schema(description = "镜像表关联数据")
    private SchTaskImage taskImage;


    /**
     * 启动任务成功之后的容器名
     */
    @Schema(description = "启动任务成功之后的容器名")
    private String containerName;
    /**
     * 编号。
     */
    @Schema(description = "编号")
    private Long id;

    /**
     * 字符id。
     */
    @Schema(description = "字符id")
    private String strId;

    /**
     * 更新时间。
     */
    @Schema(description = "更新时间")
    private Date updateTime;

    /**
     * 创建时间。
     */
    @Schema(description = "创建时间")
    private Date createTime;

    /**
     * 创建人。
     */
    @Schema(description = "创建人")
    private Long createUserId;

    /**
     * 更新人。
     */
    @Schema(description = "更新人")
    private Long updateUserId;

    /**
     * 数据所属人。
     */
    @Schema(description = "数据所属人")
    private Long dataUserId;

    /**
     * 数据所属部门。
     */
    @Schema(description = "数据所属部门")
    private Long dataDeptId;

    /**
     * 任务名称。
     */
    @Schema(description = "任务名称")
    private String taskName;

    /**
     * 任务状态（（枚举：pending待审批、queued排队中、running运行中、finished完成、failed失败、cancelled取消、rejected已拒绝）。
     */
    @Schema(description = "任务状态（（枚举：pending初始状态、queued排队中、starting启动中、running运行中、stop暂停、finished完成、failed失败）")
    private String status;

    /**
     * 优先级（1-10,默认0）。
     */
    @Schema(description = "优先级（1-10,默认0）")
    private Integer taskPriority;

    /**
     * 所需显存（MB）。
     */
    @Schema(description = "所需显存（MB）")
    private Integer graphicNeededMb;

    /**
     * 所需内存（MB）。
     */
    @Schema(description = "所需内存（MB）")
    private Integer memoryNeededMb;

    /**
     * 所需CPU核。
     */
    @Schema(description = "所需CPU核")
    private String cpuNeed;

    /**
     * 指定使用的资源池（可为空，系统自动选择）。
     */
    @Schema(description = "指定使用的资源池（可为空，系统自动选择）")
    private Long poolId;

    /**
     * 资源ID(不选资源池的时候后端自动分配节点，选了池是该池下的某个资源ID，实际运行)。
     */
    @Schema(description = "资源ID(不选资源池的时候后端自动分配节点，选了池是该池下的某个资源ID，实际运行)")
    private Long resourceId;

    /**
     * 镜像表id。
     */
    @Schema(description = "镜像表id")
    private Long taskImageId;

    /**
     * 计算卡ID(如果需要算力)。
     */
    @Schema(description = "计算卡ID(如果需要算力)")
    private Long computeDeviceId;

    /**
     * 资源切分ID vnpuId VCC_ID 不是主键id
     */
    @Schema(description = "资源切分ID  vnpuid")
    private Long partitionId;

    /**
     * 执行的命令或脚本（由用户提供，如运行什么程序）。
     */
    @Schema(description = "执行的命令或脚本（由用户提供，如运行什么程序）")
    private String runCommand;

    /**
     * 环境变量键。
     */
    @Schema(description = "环境变量键")
    private String envConfig;

    /**
     * 资源释放策略（1自动 2手动）。
     */
    @Schema(description = "资源释放策略（1自动 2手动）")
    private Integer releasePolicy;

    /**
     * 任务开始时间。
     */
    @Schema(description = "任务开始时间")
    private Date startTime;

    /**
     * 任务结束时间。
     */
    @Schema(description = "任务结束时间")
    private Date endTiem;

    /**
     * 预计执行时间（分钟）。
     */
    @Schema(description = "预计执行时间（分钟）")
    private Date estimatTime;

    /**
     * 调度策略（1优先级调度2短作业优先3紧急抢占）。
     */
    @Schema(description = "调度策略（1优先级调度2短作业优先3紧急抢占）")
    private Integer schedulingPolicies;

    /**
     * 审批状态（通过未通过）。审批状态（通过、未通过、待审核）
     */
    @Schema(description = "审批状态(通过approved、未通过rejected、待审核pending)")
    private String approveState;

    /**
     * 是否允许被抢占(1 是，-1 否)。
     */
    @Schema(description = "是否允许被抢占(1 是，-1 否)")
    private Integer allowPreemption;


    /**
     * cpu信息。
     */
    @TableField(exist = false)
    private SchNodeBasicMetrics schNodeBasicMetrics;

    /**
     * npu信息。
     */
    @TableField(exist = false)
    private SchCardMonitor schCardMonitor;


    /**
     * 当前任务执行扩缩容计划ID
     */
    @Schema(description = "当前任务执行扩缩容计划ID")
    private  Long scalePlanId;
}
