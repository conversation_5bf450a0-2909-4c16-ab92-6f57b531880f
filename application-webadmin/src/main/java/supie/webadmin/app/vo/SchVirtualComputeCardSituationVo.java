package supie.webadmin.app.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import supie.common.core.annotation.RelationOneToOne;
import supie.webadmin.app.model.SchVirtualComputeCardTemplate;

import java.util.Date;

/**
 * vpu情况表VO视图对象。
 *
 * <AUTHOR> -rf .bug
 * @date 2025-05-16
 */
@Schema(description = "vpu情况表VO视图对象")
@Data
public class SchVirtualComputeCardSituationVo {

    /**
     * 显卡id。
     */
    @Schema(description = "计算卡主键id")
    private Long computeDeviceId;


    /**
     * 主键ID。
     */
    @Schema(description = "主键ID")
    private Long id;

    /**
     * 字符串Id。
     */
    @Schema(description = "字符串Id")
    private String strId;

    /**
     * 创建者ID。
     */
    @Schema(description = "创建者ID")
    private Long createUserId;

    /**
     * 创建时间。
     */
    @Schema(description = "创建时间")
    private Date createTime;

    /**
     * 修改者ID。
     */
    @Schema(description = "修改者ID")
    private Long updateUserId;

    /**
     * 修改时间。
     */
    @Schema(description = "修改时间")
    private Date updateTime;

    /**
     * 数据所属人ID。
     */
    @Schema(description = "数据所属人ID")
    private Long dataUserId;

    /**
     * 数据所属部门ID。
     */
    @Schema(description = "数据所属部门ID")
    private Long dataDeptId;

    /**
     * 显卡id。
     */
    @Schema(description = "显卡id")
    private Long computingPowerManagementId;

    /**
     * 虚拟化实例模板id。
     */
    @Schema(description = "虚拟化实例模板id")
    private Long templateId;

    /**
     * vNPUid。
     */
    @Schema(description = "vNPUid")
    private Integer vccId;

    /**
     * 虚拟资源组vGroup的id。
     */
    @Schema(description = "虚拟资源组vGroup的id")
    private Integer vgroupId;

    /**
     * 切分模板。
     */
    @RelationOneToOne(slaveIdField = "id", slaveModelClass = SchVirtualComputeCardTemplate.class,masterIdField = "templateId")
    @TableField(exist = false)
    private SchVirtualComputeCardTemplate schVirtualComputeCardTemplate;
}
