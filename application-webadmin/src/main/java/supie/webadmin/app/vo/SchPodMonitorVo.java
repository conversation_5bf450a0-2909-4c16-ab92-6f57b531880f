package supie.webadmin.app.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import supie.common.core.annotation.RelationOneToOne;
import supie.webadmin.app.model.SchClusterNamespace;
import supie.webadmin.app.model.SchClusterNode;

import java.util.Date;

/**
 * K8S容器组件监控表VO视图对象。
 *
 * <AUTHOR> -rf .bug
 * @date 2025-05-16
 */
@Schema(description = "K8S容器组件监控表VO视图对象")
@Data
public class SchPodMonitorVo {

    /**
     * 命名空间
     */
    @Schema(description = "命名空间")
    private  SchClusterNamespace namespace;

    /**
     * 容器主键监控表主键id。
     */
    @Schema(description = "容器主键监控表主键id")
    private Long id;

    /**
     * 集群节点id。
     */
    @Schema(description = "集群节点id")
    private Long clusterNodeId;

    /**
     * 集群命名空间id。
     */
    @Schema(description = "集群命名空间id")
    private Long clusterNamespaceId;

    /**
     * 集群标签id。
     */
    @Schema(description = "集群标签id")
    private String clusterLabelId;

    /**
     * pod名称。
     */
    @Schema(description = "pod名称")
    private String podName;

    /**
     * pod运行状态(running stopped)。
     */
    @Schema(description = "pod运行状态(running stopped)")
    private String podRunStatus;

    /**
     * pod就绪状态(就绪数/总数)。
     */
    @Schema(description = "pod就绪状态(就绪数/总数)")
    private String podReady;

    /**
     * pod运行时间。
     */
    @Schema(description = "pod运行时间")
    private String podRunTime;

    /**
     * pod重启次数。
     */
    @Schema(description = "pod重启次数")
    private String podRestartCount;

    /**
     * pod内部ip地址(集群通信使用)。
     */
    @Schema(description = "pod内部ip地址(集群通信使用)")
    private String podInnerIp;

    /**
     * 配置文件。
     */
    @Schema(description = "配置文件")
    private String podConfig;

    /**
     * 扩展字段。
     */
    @Schema(description = "扩展字段")
    private String podExtends;

    /**
     * 字符编号。
     */
    @Schema(description = "字符编号")
    private String strId;

    /**
     * 更新时间。
     */
    @Schema(description = "更新时间")
    private Date updateTime;

    /**
     * 创建时间。
     */
    @Schema(description = "创建时间")
    private Date createTime;

    /**
     * 创建人。
     */
    @Schema(description = "创建人")
    private Long createUserId;

    /**
     * 更新人。
     */
    @Schema(description = "更新人")
    private Long updateUserId;

    /**
     * 数据所属人。
     */
    @Schema(description = "数据所属人")
    private Long dataUserId;

    /**
     * 数据所属部门。
     */
    @Schema(description = "数据所属部门")
    private Long dataDeptId;

    /**
     * 集群节点
     */
    @RelationOneToOne(
            masterIdField = "clusterNodeId",
            slaveIdField = "id",
            slaveModelClass = SchClusterNode.class
    )
    @TableField(exist = false)
    private  SchClusterNode schClusterNode;
}
