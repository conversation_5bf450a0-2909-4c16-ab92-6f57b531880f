package supie.webadmin.app.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 资源池信息表VO视图对象。
 *
 * <AUTHOR> -rf .bug
 * @date 2025-05-16
 */
@Schema(description = "资源池信息表VO视图对象")
@Data
public class SchResourcePoolVo {

    /**
     * 资源池成员信息列表
     */
    @Schema(description = "资源池成员信息列表")
    private List<SchResourceInfoVo> resourceInfoList;

    /**
     * 编号。
     */
    @Schema(description = "编号")
    private Long id;

    /**
     * 字符id。
     */
    @Schema(description = "字符id")
    private String strId;

    /**
     * 更新时间。
     */
    @Schema(description = "更新时间")
    private Date updateTime;

    /**
     * 创建时间。
     */
    @Schema(description = "创建时间")
    private Date createTime;

    /**
     * 创建人。
     */
    @Schema(description = "创建人")
    private Long createUserId;

    /**
     * 更新人。
     */
    @Schema(description = "更新人")
    private Long updateUserId;

    /**
     * 数据所属人。
     */
    @Schema(description = "数据所属人")
    private Long dataUserId;

    /**
     * 数据所属部门。
     */
    @Schema(description = "数据所属部门")
    private Long dataDeptId;

    /**
     * 资源池名称。
     */
    @Schema(description = "资源池名称")
    private String poolName;

    @Schema(description = "资源池占用比")
    private Map<String, Object> resourcePoolStatistics;

    /**
     * 资源池描述。
     */
    @Schema(description = "资源池描述")
    private String poolDescription;
}
