package supie.webadmin.app.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description
 * @date 2025/6/9 16:50
 */
@Data
public class ImageCountVo {

    /**
     * 总镜像数
     */
    @Schema(description = "总镜像数")
    private Integer total;

    /**
     * 最近7天更新总数
     */
    @Schema(description = "最近7天更新总数")
    private Integer relateUpdate;

    /**
     * 运行镜像总数
     */
    @Schema(description = "运行镜像总数")
    private Integer runTotal;
}
