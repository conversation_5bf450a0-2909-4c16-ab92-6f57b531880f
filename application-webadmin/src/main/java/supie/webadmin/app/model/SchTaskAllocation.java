package supie.webadmin.app.model;

import com.baomidou.mybatisplus.annotation.*;
import lombok.experimental.Accessors;
import supie.common.core.util.MyCommonUtil;
import supie.common.core.annotation.*;
import lombok.Data;

import java.util.Date;

/**
 * 任务资源实际分配表实体对象。
 *
 * <AUTHOR> -rf .bug
 * @date 2025-05-16
 */
@Accessors(chain = true)
@Data
@TableName(value = "sch_task_allocation")
public class SchTaskAllocation {

    /**
     * 编号。
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 字符id。
     */
    @TableField(value = "str_id")
    private String strId;

    /**
     * 更新时间。
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 创建时间。
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 创建人。
     */
    @TableField(value = "create_user_id")
    private Long createUserId;

    /**
     * 更新人。
     */
    @TableField(value = "update_user_id")
    private Long updateUserId;

    /**
     * 数据所属人。
     */
    @UserFilterColumn
    @TableField(value = "data_user_id")
    private Long dataUserId;

    /**
     * 数据所属部门。
     */
    @DeptFilterColumn
    @TableField(value = "data_dept_id")
    private Long dataDeptId;

    /**
     * 逻辑删除标记字段(1: 正常 -1: 已删除)。
     */
    @TableLogic
    @TableField(value = "is_delete")
    private Integer isDelete;

    /**
     * 任务ID。
     */
    @TableField(value = "task_id")
    private Long taskId;

    /**
     * 实际使用的资源 ID。
     */
    @TableField(value = "resource_id")
    private Long resourceId;

    /**
     * 计算卡的id如果用到。
     */
    @TableField(value = "compute_device_id")
    private Long computeDeviceId;

    /**
     * 实际使用的资源分区 ID。
     */
    @TableField(value = "partition_id")
    private Long partitionId;

    /**
     * 实际使用的CPU核心数。
     */
    @TableField(value = "cpu_core_number")
    private Integer cpuCoreNumber;

    /**
     * 实际使用的算力百分比。
     */
    @TableField(value = "compute_percent")
    private String computePercent;

    /**
     * 实际使用的显存。
     */
    @TableField(value = "graphics_memory")
    private String graphicsMemory;

    /**
     * 实际使用的内存。
     */
    @TableField(value = "memory")
    private String memory;

    /**
     * 资源占用开始时间。
     */
    @TableField(value = "start_time")
    private String startTime;

    /**
     * 资源占用结束时间。
     */
    @TableField(value = "end_time")
    private String endTime;

    /**
     * updateTime 范围过滤起始值(>=)。
     */
    @TableField(exist = false)
    private String updateTimeStart;

    /**
     * updateTime 范围过滤结束值(<=)。
     */
    @TableField(exist = false)
    private String updateTimeEnd;

    /**
     * createTime 范围过滤起始值(>=)。
     */
    @TableField(exist = false)
    private String createTimeStart;

    /**
     * createTime 范围过滤结束值(<=)。
     */
    @TableField(exist = false)
    private String createTimeEnd;

    /**
     * str_id / compute_percent / graphics_memory / memory / start_time / end_time LIKE搜索字符串。
     */
    @TableField(exist = false)
    private String searchString;

    public void setSearchString(String searchString) {
        this.searchString = MyCommonUtil.replaceSqlWildcard(searchString);
    }
}
