package supie.webadmin.app.model;

import com.baomidou.mybatisplus.annotation.*;
import supie.common.core.util.MyCommonUtil;
import supie.common.core.annotation.*;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 计算卡表（GPU、NPU）实体对象。
 *
 * <AUTHOR> -rf .bug
 * @date 2025-05-16
 */
@Data
@TableName(value = "sch_compute_device")
public class SchComputeDevice {

    /**
     * 编号。
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 字符id。
     */
    @TableField(value = "str_id")
    private String strId;

    /**
     * 更新时间。
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 创建时间。
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 创建人。
     */
    @TableField(value = "create_user_id")
    private Long createUserId;

    /**
     * 更新人。
     */
    @TableField(value = "update_user_id")
    private Long updateUserId;

    /**
     * 数据所属人。
     */
    @UserFilterColumn
    @TableField(value = "data_user_id")
    private Long dataUserId;

    /**
     * 数据所属部门。
     */
    @DeptFilterColumn
    @TableField(value = "data_dept_id")
    private Long dataDeptId;

    /**
     * 逻辑删除标记字段(1: 正常 -1: 已删除)。
     */
    @TableLogic
    @TableField(value = "is_delete")
    private Integer isDelete;

    /**
     * 所属资源ID，关联 resource 表。
     */
    @TableField(value = "resource_id")
    private Long resourceId;

    /**
     * 状态（空闲、已分配、故障）。
     */
    @TableField(value = "status")
    private String status;

    /**
     * 总算力（TFLOPS）。
     */
    @TableField(value = "total_compute")
    private String totalCompute;

    /**
     * GPU / NPU。
     */
    @TableField(value = "device_type")
    private String deviceType;

    /**
     * 型号如A100。
     */
    @TableField(value = "model_number")
    private String modelNumber;

    /**
     * 标识（名字）：如A100-80GB-SN123。
     */
    @TableField(value = "device_name")
    private String deviceName;

    /**
     * 显存大小（GB）。
     */
    @TableField(value = "memory_size")
    private Integer memorySize;

    /**
     * 半精度(TFLOPS)。
     */
    @TableField(value = "semi_precision")
    private BigDecimal semiPrecision;

    /**
     * 单精度(TFLOPS)。
     */
    @TableField(value = "single_precision")
    private BigDecimal singlePrecision;

    /**
     * 双精度(TFLOPS)。
     */
    @TableField(value = "double_precision")
    private BigDecimal doublePrecision;

    /**
     * CUDA核心数量。
     */
    @TableField(value = "cuda_number")
    private Integer cudaNumber;

    /**
     * Tensor核心数量。
     */
    @TableField(value = "tensor_number")
    private Integer tensorNumber;

    /**
     * 架构。
     */
    @TableField(value = "architecture")
    private String architecture;

    /**
     * 产品型号（1：Ascend 910，2：Atlas 推理系列产品）
     */
    @TableField(value = "product_type")
    private Integer productType;

    /**
     * 卡编号。
     */
    @TableField(value = "device_number")
    private Integer deviceNumber;

    @TableField(value = "card_type")
    private String cardType;


    /**
     * 显存类型。
     */
    @TableField(value = "memory_type")
    private String memoryType;

    /**
     * updateTime 范围过滤起始值(>=)。
     */
    @TableField(exist = false)
    private String updateTimeStart;

    /**
     * updateTime 范围过滤结束值(<=)。
     */
    @TableField(exist = false)
    private String updateTimeEnd;

    /**
     * createTime 范围过滤起始值(>=)。
     */
    @TableField(exist = false)
    private String createTimeStart;

    /**
     * createTime 范围过滤结束值(<=)。
     */
    @TableField(exist = false)
    private String createTimeEnd;

    /**
     * status / device_type / model_number / device_name LIKE搜索字符串。
     */
    @TableField(exist = false)
    private String searchString;

    @RelationOneToMany(slaveIdField = "computingPowerManagementId", slaveModelClass = SchVirtualComputeCardSituation.class,masterIdField = "id")
    @TableField(exist = false)
    private List<SchVirtualComputeCardSituation> schVirtualComputeCardSituationList;

    public void setSearchString(String searchString) {
        this.searchString = MyCommonUtil.replaceSqlWildcard(searchString);
    }
}
