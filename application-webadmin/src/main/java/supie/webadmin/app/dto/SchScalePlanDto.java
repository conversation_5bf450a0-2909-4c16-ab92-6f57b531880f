package supie.webadmin.app.dto;

import supie.common.core.validator.UpdateGroup;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.*;

/**
 * 扩缩容执行计划表Dto对象。
 *
 * <AUTHOR> -rf .bug
 * @date 2025-05-16
 */
@Schema(description = "扩缩容执行计划表Dto对象")
@Data
public class SchScalePlanDto {

    /**
     * 任务ID
     */
    @Schema(description = "任务ID")
    private  String taskId;

    /**
     * 扩缩容执行计划表主键id。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "扩缩容执行计划表主键id。可支持等于操作符的列表数据过滤。", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "数据验证失败，扩缩容执行计划表主键id不能为空！", groups = {UpdateGroup.class})
    private Long id;

    /**
     * 扩容CPU核数。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "扩容CPU核数。可支持等于操作符的列表数据过滤。")
    private Integer expandCoreNum;

    /**
     * 扩容内存数。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "扩容内存数。可支持等于操作符的列表数据过滤。")
    private Long expandMemoryNum;

    /**
     * CPU扩容阈值。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "CPU扩容阈值。可支持等于操作符的列表数据过滤。")
    private Integer cpuThresholdEx;

    /**
     * 内存扩容阈值。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "内存扩容阈值。可支持等于操作符的列表数据过滤。")
    private Integer memoryThresholdEx;

    /**
     * 显存扩容阈值。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "显存扩容阈值。可支持等于操作符的列表数据过滤。")
    private Integer graphicThresholdEx;

    /**
     * CPU缩容阈值。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "CPU缩容阈值。可支持等于操作符的列表数据过滤。")
    private Integer cpuThresholdDc;

    /**
     * 内存缩容阈值。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "内存缩容阈值。可支持等于操作符的列表数据过滤。")
    private Integer memoryThresholdDc;

    /**
     * 显存缩容阈值。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "显存缩容阈值。可支持等于操作符的列表数据过滤。")
    private Integer graphicThresholdDc;

    /**
     * 操作类型(扩容 缩容)。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "操作类型(扩容 缩容)。可支持等于操作符的列表数据过滤。")
    private String operationType;

    /**
     * 资源配置(json)节点卡信息。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "资源配置(json)节点卡信息。可支持等于操作符的列表数据过滤。")
    private String resourceConfig;

    /**
     * 字符编号。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "字符编号。可支持等于操作符的列表数据过滤。")
    private String strId;

    /**
     * 数据所属人。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "数据所属人。可支持等于操作符的列表数据过滤。")
    private Long dataUserId;

    /**
     * 数据所属部门。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "数据所属部门。可支持等于操作符的列表数据过滤。")
    private Long dataDeptId;

    /**
     * updateTime 范围过滤起始值(>=)。
     * NOTE: 可支持范围操作符的列表数据过滤。
     */
    @Schema(description = "updateTime 范围过滤起始值(>=)。可支持范围操作符的列表数据过滤。")
    private String updateTimeStart;

    /**
     * updateTime 范围过滤结束值(<=)。
     * NOTE: 可支持范围操作符的列表数据过滤。
     */
    @Schema(description = "updateTime 范围过滤结束值(<=)。可支持范围操作符的列表数据过滤。")
    private String updateTimeEnd;

    /**
     * createTime 范围过滤起始值(>=)。
     * NOTE: 可支持范围操作符的列表数据过滤。
     */
    @Schema(description = "createTime 范围过滤起始值(>=)。可支持范围操作符的列表数据过滤。")
    private String createTimeStart;

    /**
     * createTime 范围过滤结束值(<=)。
     * NOTE: 可支持范围操作符的列表数据过滤。
     */
    @Schema(description = "createTime 范围过滤结束值(<=)。可支持范围操作符的列表数据过滤。")
    private String createTimeEnd;

    /**
     * operation_type / resource_config LIKE搜索字符串。
     * NOTE: 可支持LIKE操作符的列表数据过滤。
     */
    @Schema(description = "LIKE模糊搜索字符串")
    private String searchString;
}
