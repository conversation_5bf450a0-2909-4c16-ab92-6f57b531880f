package supie.webadmin.app.dto;

import supie.common.core.validator.UpdateGroup;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.*;

/**
 * K8SIngress路由管理表Dto对象。
 *
 * <AUTHOR> -rf .bug
 * @date 2025-05-16
 */
@Schema(description = "K8SIngress路由管理表Dto对象")
@Data
public class SchIngressDto {

    /**
     * Ingress主键id。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "Ingress主键id。可支持等于操作符的列表数据过滤。", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "数据验证失败，Ingress主键id不能为空！", groups = {UpdateGroup.class})
    private Long id;

    /**
     * Ingress名称。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "Ingress名称。可支持等于操作符的列表数据过滤。", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "数据验证失败，Ingress名称不能为空！")
    private String ingressName;

    /**
     * 命名空间ID。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "命名空间ID。可支持等于操作符的列表数据过滤。")
    private Long namespaceId;

    /**
     * 集群ID。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "集群ID。可支持等于操作符的列表数据过滤。")
    private Long clusterId;

    /**
     * Ingress类名称。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "Ingress类名称。可支持等于操作符的列表数据过滤。")
    private String ingressClassName;

    /**
     * TLS配置(JSON格式)。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "TLS配置(JSON格式)。可支持等于操作符的列表数据过滤。")
    private String tlsConfig;

    /**
     * 注解(JSON格式)。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "注解(JSON格式)。可支持等于操作符的列表数据过滤。")
    private String annotations;

    /**
     * 路由规则(JSON格式)。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "路由规则(JSON格式)。可支持等于操作符的列表数据过滤。", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "数据验证失败，路由规则(JSON格式)不能为空！")
    private String rules;

    /**
     * Ingress状态。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "Ingress状态。可支持等于操作符的列表数据过滤。")
    private String status;

    /**
     * 字符id。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "字符id。可支持等于操作符的列表数据过滤。")
    private String strId;

    /**
     * 数据所属人。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "数据所属人。可支持等于操作符的列表数据过滤。")
    private Long dataUserId;

    /**
     * 数据所属部门。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "数据所属部门。可支持等于操作符的列表数据过滤。")
    private Long dataDeptId;

    /**
     * updateTime 范围过滤起始值(>=)。
     * NOTE: 可支持范围操作符的列表数据过滤。
     */
    @Schema(description = "updateTime 范围过滤起始值(>=)。可支持范围操作符的列表数据过滤。")
    private String updateTimeStart;

    /**
     * updateTime 范围过滤结束值(<=)。
     * NOTE: 可支持范围操作符的列表数据过滤。
     */
    @Schema(description = "updateTime 范围过滤结束值(<=)。可支持范围操作符的列表数据过滤。")
    private String updateTimeEnd;

    /**
     * createTime 范围过滤起始值(>=)。
     * NOTE: 可支持范围操作符的列表数据过滤。
     */
    @Schema(description = "createTime 范围过滤起始值(>=)。可支持范围操作符的列表数据过滤。")
    private String createTimeStart;

    /**
     * createTime 范围过滤结束值(<=)。
     * NOTE: 可支持范围操作符的列表数据过滤。
     */
    @Schema(description = "createTime 范围过滤结束值(<=)。可支持范围操作符的列表数据过滤。")
    private String createTimeEnd;

    /**
     * ingress_name / ingress_class_name / tls_config / annotations / rules / status LIKE搜索字符串。
     * NOTE: 可支持LIKE操作符的列表数据过滤。
     */
    @Schema(description = "LIKE模糊搜索字符串")
    private String searchString;
}
