package supie.webadmin.app.dto;

import supie.common.core.validator.UpdateGroup;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.*;

/**
 * K8S Service端口配置表Dto对象。
 *
 * <AUTHOR> -rf .bug
 * @date 2025-05-16
 */
@Schema(description = "K8S Service端口配置表Dto对象")
@Data
public class SchServicePortDto {

    /**
     * 端口配置主键id。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "端口配置主键id。可支持等于操作符的列表数据过滤。", requiredMode = Schema.RequiredMode.REQUIRED)

    private Long id;

    /**
     * 服务ID。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "服务ID。可支持等于操作符的列表数据过滤。")
    private Long serviceId;

    /**
     * 服务端口。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "服务端口。可支持等于操作符的列表数据过滤。", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer port;

    /**
     * 目标端口。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "目标端口。可支持等于操作符的列表数据过滤。", requiredMode = Schema.RequiredMode.REQUIRED)
    private String targetPort;

    /**
     * 节点端口(NodePort类型时使用)。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "节点端口(NodePort类型时使用)。可支持等于操作符的列表数据过滤。")
    private Integer nodePort;

    /**
     * 协议(TCP/UDP/SCTP)。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "协议(TCP/UDP/SCTP)。可支持等于操作符的列表数据过滤。")
    private String protocol;

    /**
     * 端口名称。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "端口名称。可支持等于操作符的列表数据过滤。")
    private String name;

    /**
     * 字符id。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "字符id。可支持等于操作符的列表数据过滤。")
    private String strId;

    /**
     * 数据所属人。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "数据所属人。可支持等于操作符的列表数据过滤。")
    private Long dataUserId;

    /**
     * 数据所属部门。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "数据所属部门。可支持等于操作符的列表数据过滤。")
    private Long dataDeptId;

    /**
     * updateTime 范围过滤起始值(>=)。
     * NOTE: 可支持范围操作符的列表数据过滤。
     */
    @Schema(description = "updateTime 范围过滤起始值(>=)。可支持范围操作符的列表数据过滤。")
    private String updateTimeStart;

    /**
     * updateTime 范围过滤结束值(<=)。
     * NOTE: 可支持范围操作符的列表数据过滤。
     */
    @Schema(description = "updateTime 范围过滤结束值(<=)。可支持范围操作符的列表数据过滤。")
    private String updateTimeEnd;

    /**
     * createTime 范围过滤起始值(>=)。
     * NOTE: 可支持范围操作符的列表数据过滤。
     */
    @Schema(description = "createTime 范围过滤起始值(>=)。可支持范围操作符的列表数据过滤。")
    private String createTimeStart;

    /**
     * createTime 范围过滤结束值(<=)。
     * NOTE: 可支持范围操作符的列表数据过滤。
     */
    @Schema(description = "createTime 范围过滤结束值(<=)。可支持范围操作符的列表数据过滤。")
    private String createTimeEnd;

    /**
     * target_port / protocol / name LIKE搜索字符串。
     * NOTE: 可支持LIKE操作符的列表数据过滤。
     */
    @Schema(description = "LIKE模糊搜索字符串")
    private String searchString;
}
