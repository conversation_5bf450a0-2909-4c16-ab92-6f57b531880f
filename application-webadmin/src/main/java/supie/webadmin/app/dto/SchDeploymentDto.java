package supie.webadmin.app.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import supie.common.core.validator.UpdateGroup;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.*;

/**
 * k8s部署管理Dto对象。
 *
 * <AUTHOR> -rf .bug
 * @date 2025-05-16
 */
@Schema(description = "k8s部署管理Dto对象")
@Data
public class SchDeploymentDto {

    /**
     * 部署ID
     */
    @Schema(description = "部署ID")
    private String deploymentId;

    /**
     * 部署id。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "部署id。可支持等于操作符的列表数据过滤。", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "数据验证失败，部署id不能为空！", groups = {UpdateGroup.class})
    private String id;

    /**
     * 集群id。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "集群id。可支持等于操作符的列表数据过滤。")
    private String clusterManagerId;

    /**
     * 集群管理表主键id。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "集群管理表主键id。可支持等于操作符的列表数据过滤。")
    private Long namespaceId;

    /**
     * 集群标签
     */
    @Schema(description = "集权标签")
    private String clusterLabel;

    /**
     * 镜像id。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "镜像id。可支持等于操作符的列表数据过滤。")
    private Long taskImageId;

    /**
     * 部署显示名称。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "部署显示名称。可支持等于操作符的列表数据过滤。")
    private String displayName;

    /**
     * 部署描述 (可选)。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "部署描述 (可选)。可支持等于操作符的列表数据过滤。")
    private String description;

    /**
     * 状态 (Activity, Terminating,Pending, Failed)。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "状态 (Activity, Terminating,Pending, Failed)。可支持等于操作符的列表数据过滤。", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "数据验证失败，状态 (Activity, Terminating,Pending, Failed)不能为空！")
    private String status;

    /**
     * pod数量。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "pod数量。可支持等于操作符的列表数据过滤。")
    private Integer podCount;

    /**
     * 当前pod运行副本数。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "当前pod运行副本数。可支持等于操作符的列表数据过滤。")
    private Integer currentPodReplicas;

    /**
     * 当前可用pod副本数。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "当前可用pod副本数。可支持等于操作符的列表数据过滤。")
    private Integer availablePodReplicas;

    /**
     * 更新策略。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "更新策略。可支持等于操作符的列表数据过滤。")
    private String strategy;

    /**
     * 运行时间。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "运行时间。可支持等于操作符的列表数据过滤。")
    private String runTime;

    /**
     * pod配置 (json)。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "pod配置 (json)。可支持等于操作符的列表数据过滤。")
    private String podConfig;

    /**
     * 运行时长。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "运行时长。可支持等于操作符的列表数据过滤。", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "数据验证失败，运行时长不能为空！")
    private String age;

    /**
     * 字符id。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "字符id。可支持等于操作符的列表数据过滤。")
    private String strId;

    /**
     * 数据所属人。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "数据所属人。可支持等于操作符的列表数据过滤。")
    private Long dataUserId;

    /**
     * 数据所属部门。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "数据所属部门。可支持等于操作符的列表数据过滤。")
    private Long dataDeptId;

    /**
     * updateTime 范围过滤起始值(>=)。
     * NOTE: 可支持范围操作符的列表数据过滤。
     */
    @Schema(description = "updateTime 范围过滤起始值(>=)。可支持范围操作符的列表数据过滤。")
    private String updateTimeStart;

    /**
     * updateTime 范围过滤结束值(<=)。
     * NOTE: 可支持范围操作符的列表数据过滤。
     */
    @Schema(description = "updateTime 范围过滤结束值(<=)。可支持范围操作符的列表数据过滤。")
    private String updateTimeEnd;

    /**
     * createTime 范围过滤起始值(>=)。
     * NOTE: 可支持范围操作符的列表数据过滤。
     */
    @Schema(description = "createTime 范围过滤起始值(>=)。可支持范围操作符的列表数据过滤。")
    private String createTimeStart;

    /**
     * createTime 范围过滤结束值(<=)。
     * NOTE: 可支持范围操作符的列表数据过滤。
     */
    @Schema(description = "createTime 范围过滤结束值(<=)。可支持范围操作符的列表数据过滤。")
    private String createTimeEnd;

    /**
     * display_name / description / status / strategy / run_time / pod_config / age LIKE搜索字符串。
     * NOTE: 可支持LIKE操作符的列表数据过滤。
     */
    @Schema(description = "LIKE模糊搜索字符串")
    private String searchString;
}
