package supie.webadmin.app.dto;

import supie.common.core.validator.UpdateGroup;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.*;

import java.util.Date;

/**
 * 任务资源实际分配表Dto对象。
 *
 * <AUTHOR> -rf .bug
 * @date 2025-05-16
 */
@Schema(description = "任务资源实际分配表Dto对象")
@Data
public class SchTaskAllocationDto {

    /**
     * 编号。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "编号。可支持等于操作符的列表数据过滤。", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "数据验证失败，编号不能为空！", groups = {UpdateGroup.class})
    private Long id;

    /**
     * 字符id。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "字符id。可支持等于操作符的列表数据过滤。")
    private String strId;

    /**
     * 更新时间。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "更新时间。可支持等于操作符的列表数据过滤。")
    private Date updateTime;

    /**
     * 创建时间。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "创建时间。可支持等于操作符的列表数据过滤。")
    private Date createTime;

    /**
     * 创建人。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "创建人。可支持等于操作符的列表数据过滤。")
    private Long createUserId;

    /**
     * 更新人。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "更新人。可支持等于操作符的列表数据过滤。")
    private Long updateUserId;

    /**
     * 数据所属人。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "数据所属人。可支持等于操作符的列表数据过滤。")
    private Long dataUserId;

    /**
     * 数据所属部门。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "数据所属部门。可支持等于操作符的列表数据过滤。")
    private Long dataDeptId;

    /**
     * 任务ID。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "任务ID。可支持等于操作符的列表数据过滤。")
    private Long taskId;

    /**
     * 实际使用的资源 ID。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "实际使用的资源 ID。可支持等于操作符的列表数据过滤。")
    private Long resourceId;

    /**
     * 计算卡的id如果用到。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "计算卡的id如果用到。可支持等于操作符的列表数据过滤。")
    private Long computeDeviceId;

    /**
     * 实际使用的资源分区 ID。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "实际使用的资源分区 ID。可支持等于操作符的列表数据过滤。")
    private Long partitionId;

    /**
     * 实际使用的CPU核心数。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "实际使用的CPU核心数。可支持等于操作符的列表数据过滤。")
    private Integer cpuCoreNumber;

    /**
     * 实际使用的算力百分比。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "实际使用的算力百分比。可支持等于操作符的列表数据过滤。")
    private String computePercent;

    /**
     * 实际使用的显存。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "实际使用的显存。可支持等于操作符的列表数据过滤。")
    private String graphicsMemory;

    /**
     * 实际使用的内存。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "实际使用的内存。可支持等于操作符的列表数据过滤。")
    private String memory;

    /**
     * 资源占用开始时间。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "资源占用开始时间。可支持等于操作符的列表数据过滤。")
    private String startTime;

    /**
     * 资源占用结束时间。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "资源占用结束时间。可支持等于操作符的列表数据过滤。")
    private String endTime;

    /**
     * updateTime 范围过滤起始值(>=)。
     * NOTE: 可支持范围操作符的列表数据过滤。
     */
    @Schema(description = "updateTime 范围过滤起始值(>=)。可支持范围操作符的列表数据过滤。")
    private String updateTimeStart;

    /**
     * updateTime 范围过滤结束值(<=)。
     * NOTE: 可支持范围操作符的列表数据过滤。
     */
    @Schema(description = "updateTime 范围过滤结束值(<=)。可支持范围操作符的列表数据过滤。")
    private String updateTimeEnd;

    /**
     * createTime 范围过滤起始值(>=)。
     * NOTE: 可支持范围操作符的列表数据过滤。
     */
    @Schema(description = "createTime 范围过滤起始值(>=)。可支持范围操作符的列表数据过滤。")
    private String createTimeStart;

    /**
     * createTime 范围过滤结束值(<=)。
     * NOTE: 可支持范围操作符的列表数据过滤。
     */
    @Schema(description = "createTime 范围过滤结束值(<=)。可支持范围操作符的列表数据过滤。")
    private String createTimeEnd;

    /**
     * str_id / compute_percent / graphics_memory / memory / start_time / end_time LIKE搜索字符串。
     * NOTE: 可支持LIKE操作符的列表数据过滤。
     */
    @Schema(description = "LIKE模糊搜索字符串")
    private String searchString;
}
