package supie.webadmin.app.dto;


import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import supie.common.core.validator.UpdateGroup;

/**
 * 基础附件表Dto对象。
 *
 * <AUTHOR> -rf .bug
 * @date 2024-01-02
 */
@Schema(description = "BaseFilesDto对象")
@Data
public class BaseFilesDto {

    /**
     * 主键ID。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "主键ID。可支持等于操作符的列表数据过滤。", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "数据验证失败，主键ID不能为空！", groups = {UpdateGroup.class})
    private Long id;

    /**
     * 文件的 SHA-256 哈希值（用于验证文件完整性）。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "文件的 SHA-256 哈希值（用于验证文件完整性）。可支持等于操作符的列表数据过滤。", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "数据验证失败，文件的 SHA-256 哈希值（用于验证文件完整性）不能为空！")
    private String fileHash;

    /**
     * 文件名。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "文件名。可支持等于操作符的列表数据过滤。", requiredMode = Schema.RequiredMode.REQUIRED)
    private String fileName;

    /**
     * 文件的总大小（以字节为单位）。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "文件的总大小（以字节为单位）。可支持等于操作符的列表数据过滤。", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long fileSize;

    /**
     * 文件的总分片数。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "文件的总分片数。可支持等于操作符的列表数据过滤。", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer totalChunks;

    /**
     * 已成功上传的分片数。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "已成功上传的分片数。可支持等于操作符的列表数据过滤。", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer uploadedChunks;

    /**
     * 文件上传的状态（-1：失败，1：正在上传，0：已完成）。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "文件上传的状态（-1：失败，1：正在上传，0：已完成）。可支持等于操作符的列表数据过滤。", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer uploadState;

    /**
     * fileSize 范围过滤起始值(>=)。
     * NOTE: 可支持范围操作符的列表数据过滤。
     */
    @Schema(description = "fileSize 范围过滤起始值(>=)。可支持范围操作符的列表数据过滤。")
    private Long fileSizeStart;

    /**
     * fileSize 范围过滤结束值(<=)。
     * NOTE: 可支持范围操作符的列表数据过滤。
     */
    @Schema(description = "fileSize 范围过滤结束值(<=)。可支持范围操作符的列表数据过滤。")
    private Long fileSizeEnd;

    /**
     * totalChunks 范围过滤起始值(>=)。
     * NOTE: 可支持范围操作符的列表数据过滤。
     */
    @Schema(description = "totalChunks 范围过滤起始值(>=)。可支持范围操作符的列表数据过滤。")
    private Integer totalChunksStart;

    /**
     * totalChunks 范围过滤结束值(<=)。
     * NOTE: 可支持范围操作符的列表数据过滤。
     */
    @Schema(description = "totalChunks 范围过滤结束值(<=)。可支持范围操作符的列表数据过滤。")
    private Integer totalChunksEnd;

    /**
     * uploadedChunks 范围过滤起始值(>=)。
     * NOTE: 可支持范围操作符的列表数据过滤。
     */
    @Schema(description = "uploadedChunks 范围过滤起始值(>=)。可支持范围操作符的列表数据过滤。")
    private Integer uploadedChunksStart;

    /**
     * uploadedChunks 范围过滤结束值(<=)。
     * NOTE: 可支持范围操作符的列表数据过滤。
     */
    @Schema(description = "uploadedChunks 范围过滤结束值(<=)。可支持范围操作符的列表数据过滤。")
    private Integer uploadedChunksEnd;

    /**
     * createTime 范围过滤起始值(>=)。
     * NOTE: 可支持范围操作符的列表数据过滤。
     */
    @Schema(description = "createTime 范围过滤起始值(>=)。可支持范围操作符的列表数据过滤。")
    private String createTimeStart;

    /**
     * createTime 范围过滤结束值(<=)。
     * NOTE: 可支持范围操作符的列表数据过滤。
     */
    @Schema(description = "createTime 范围过滤结束值(<=)。可支持范围操作符的列表数据过滤。")
    private String createTimeEnd;

    /**
     * updateTime 范围过滤起始值(>=)。
     * NOTE: 可支持范围操作符的列表数据过滤。
     */
    @Schema(description = "updateTime 范围过滤起始值(>=)。可支持范围操作符的列表数据过滤。")
    private String updateTimeStart;

    /**
     * updateTime 范围过滤结束值(<=)。
     * NOTE: 可支持范围操作符的列表数据过滤。
     */
    @Schema(description = "updateTime 范围过滤结束值(<=)。可支持范围操作符的列表数据过滤。")
    private String updateTimeEnd;

    /**
     * file_name LIKE搜索字符串。
     * NOTE: 可支持LIKE操作符的列表数据过滤。
     */
    @Schema(description = "LIKE模糊搜索字符串")
    private String searchString;
}
