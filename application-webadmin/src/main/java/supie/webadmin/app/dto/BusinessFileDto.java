package supie.webadmin.app.dto;


import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import supie.common.core.validator.UpdateGroup;

/**
 * 业务附件表Dto对象。
 *
 * <AUTHOR> -rf .bug
 * @date 2025-02-17
 */
@Schema(description = "业务附件表Dto对象")
@Data
public class BusinessFileDto {

    /**
     * 编号。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "编号。可支持等于操作符的列表数据过滤。", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "数据验证失败，编号不能为空！", groups = {UpdateGroup.class})
    private Long id;

    /**
     * 字符编号。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "字符编号。可支持等于操作符的列表数据过滤。")
    private String strId;

    /**
     * 数据所属人。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "数据所属人。可支持等于操作符的列表数据过滤。")
    private Long dataUserId;

    /**
     * 数据所属部门。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "数据所属部门。可支持等于操作符的列表数据过滤。")
    private Long dataDeptId;

    /**
     * 文件名称。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "文件名称。可支持等于操作符的列表数据过滤。")
    private String fileName;

    /**
     * json字段。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "json字段。可支持等于操作符的列表数据过滤。")
    private String fileJson;

    /**
     * 绑定字符id。
     */
    private String bindStrId;

    /**
     * 绑定类型。
     */
    private String bindType;

    /**
     * 文件大小。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "文件大小。可支持等于操作符的列表数据过滤。")
    private Long fileSize;

    /**
     * 文件类型。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "文件类型。可支持等于操作符的列表数据过滤。")
    private String fileType;

    /**
     * 文件扩展类型。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "文件扩展类型。可支持等于操作符的列表数据过滤。")
    private String fileFormat;

    /**
     * 备注。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "备注。可支持等于操作符的列表数据过滤。")
    private String fileRemark;

    /**
     * 是否图片。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "是否图片。可支持等于操作符的列表数据过滤。")
    private String asImage;

    /**
     * updateTime 范围过滤起始值(>=)。
     * NOTE: 可支持范围操作符的列表数据过滤。
     */
    @Schema(description = "updateTime 范围过滤起始值(>=)。可支持范围操作符的列表数据过滤。")
    private String updateTimeStart;

    /**
     * updateTime 范围过滤结束值(<=)。
     * NOTE: 可支持范围操作符的列表数据过滤。
     */
    @Schema(description = "updateTime 范围过滤结束值(<=)。可支持范围操作符的列表数据过滤。")
    private String updateTimeEnd;

    /**
     * createTime 范围过滤起始值(>=)。
     * NOTE: 可支持范围操作符的列表数据过滤。
     */
    @Schema(description = "createTime 范围过滤起始值(>=)。可支持范围操作符的列表数据过滤。")
    private String createTimeStart;

    /**
     * createTime 范围过滤结束值(<=)。
     * NOTE: 可支持范围操作符的列表数据过滤。
     */
    @Schema(description = "createTime 范围过滤结束值(<=)。可支持范围操作符的列表数据过滤。")
    private String createTimeEnd;

    /**
     * fileSize 范围过滤起始值(>=)。
     * NOTE: 可支持范围操作符的列表数据过滤。
     */
    @Schema(description = "fileSize 范围过滤起始值(>=)。可支持范围操作符的列表数据过滤。")
    private Long fileSizeStart;

    /**
     * fileSize 范围过滤结束值(<=)。
     * NOTE: 可支持范围操作符的列表数据过滤。
     */
    @Schema(description = "fileSize 范围过滤结束值(<=)。可支持范围操作符的列表数据过滤。")
    private Long fileSizeEnd;

    /**
     * file_name / file_json / file_type / file_format / file_remark / as_image LIKE搜索字符串。
     * NOTE: 可支持LIKE操作符的列表数据过滤。
     */
    @Schema(description = "LIKE模糊搜索字符串")
    private String searchString;
}
