package supie.webadmin.app.dto;

import lombok.Data;
import supie.webadmin.app.model.SchComputeDevice;
import supie.webadmin.app.model.SchResourceInfo;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 资源分配信息结果类
 * @date 2025/6/18 10:43
 */
@Data
public class ResourceAllocation {
    /**
     * 服务资源对象
     */
    private SchResourceInfo resourceInfo;
    /**
     * 计算设备对象
     */
    private SchComputeDevice computeDevice;
    /**
     * 资源id
     */
    private Long poolId;
    /**
     * 分区id
     */
    private Integer partitionId;
    /**
     * 端口分配列表
     */
    private List<String> ports ;
}
