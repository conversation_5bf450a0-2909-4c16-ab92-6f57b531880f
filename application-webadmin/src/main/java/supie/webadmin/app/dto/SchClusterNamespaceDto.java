package supie.webadmin.app.dto;

import supie.common.core.validator.UpdateGroup;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.*;

/**
 * 集群命名空间管理Dto对象。
 *
 * <AUTHOR> -rf .bug
 * @date 2025-05-16
 */
@Schema(description = "集群命名空间管理Dto对象")
@Data
public class SchClusterNamespaceDto {

    /**
     * 集群命名空间管理主键id。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "集群命名空间管理主键id。可支持等于操作符的列表数据过滤。", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "数据验证失败，集群命名空间管理主键id不能为空！", groups = {UpdateGroup.class})
    private Long id;

    /**
     * 命名空间名称。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "命名空间名称。可支持等于操作符的列表数据过滤。")
    private String namespaceName;

    /**
     * 命名空间描述。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "命名空间描述。可支持等于操作符的列表数据过滤。")
    private String namespaceDescription;

    /**
     * 命名空间状态(enable disable)。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "命名空间状态(enable disable)。可支持等于操作符的列表数据过滤。")
    private String namespaceStatus;

    /**
     * 命名空间运行时间。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "命名空间运行时间。可支持等于操作符的列表数据过滤。")
    private String namespaceRunTime;

    /**
     * 字符编号。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "字符编号。可支持等于操作符的列表数据过滤。")
    private String strId;

    /**
     * 数据所属人。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "数据所属人。可支持等于操作符的列表数据过滤。")
    private Long dataUserId;

    /**
     * 数据所属部门。
     * NOTE: 可支持等于操作符的列表数据过滤。
     */
    @Schema(description = "数据所属部门。可支持等于操作符的列表数据过滤。")
    private Long dataDeptId;

    /**
     * updateTime 范围过滤起始值(>=)。
     * NOTE: 可支持范围操作符的列表数据过滤。
     */
    @Schema(description = "updateTime 范围过滤起始值(>=)。可支持范围操作符的列表数据过滤。")
    private String updateTimeStart;

    /**
     * updateTime 范围过滤结束值(<=)。
     * NOTE: 可支持范围操作符的列表数据过滤。
     */
    @Schema(description = "updateTime 范围过滤结束值(<=)。可支持范围操作符的列表数据过滤。")
    private String updateTimeEnd;

    /**
     * createTime 范围过滤起始值(>=)。
     * NOTE: 可支持范围操作符的列表数据过滤。
     */
    @Schema(description = "createTime 范围过滤起始值(>=)。可支持范围操作符的列表数据过滤。")
    private String createTimeStart;

    /**
     * createTime 范围过滤结束值(<=)。
     * NOTE: 可支持范围操作符的列表数据过滤。
     */
    @Schema(description = "createTime 范围过滤结束值(<=)。可支持范围操作符的列表数据过滤。")
    private String createTimeEnd;

    /**
     * namespace_name / namespace_description / namespace_status LIKE搜索字符串。
     * NOTE: 可支持LIKE操作符的列表数据过滤。
     */
    @Schema(description = "LIKE模糊搜索字符串")
    private String searchString;
}
