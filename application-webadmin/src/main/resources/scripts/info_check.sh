#!/bin/bash
echo "{"
# 1. 获取系统信息
echo "  \"systemInfo\": \"$(uname -s 2>/dev/null || echo 'Unknown')\","

# 2. 获取系统版本
echo -n "  \"systemVersion\": \""
if [ -f /etc/os-release ]; then
    grep '^PRETTY_NAME=' /etc/os-release | cut -d'=' -f2 | tr -d '"' | tr -d '\n'
elif [ -f /etc/redhat-release ]; then
    cat /etc/redhat-release | tr -d '\n'
elif [ -f /etc/lsb-release ]; then
    grep '^DISTRIB_DESCRIPTION=' /etc/lsb-release | cut -d'=' -f2 | tr -d '"' | tr -d '\n'
else
    uname -sr | tr -d '\n'
fi 2>/dev/null || echo -n 'Unknown'
echo "\","

# 3. 获取CPU核心数
echo "  \"cpuCoreCount\": \"$(nproc 2>/dev/null || grep -c '^processor' /proc/cpuinfo 2>/dev/null || echo 'Unknown')\","

# 4. 获取内存信息（总内存、已使用内存、剩余内存）
echo -n "  \"totalMemory\": \""
if [ -f /proc/meminfo ]; then
    awk '/MemTotal/ {printf "%.1fMB", $2/1024}' /proc/meminfo
else
    echo -n 'Unknown'
fi 2>/dev/null || echo -n 'Unknown'
echo "\","

echo -n "  \"usedMemory\": \""
if [ -f /proc/meminfo ]; then
    awk '
    /MemTotal/ { total = $2 }
    /MemAvailable/ { available = $2 }
    END {
        if (total && available) {
            used = total - available
            printf "%.1fMB", used/1024
        } else {
            print "Unknown"
        }
    }' /proc/meminfo | tr -d '\n'
else
    echo -n 'Unknown'
fi 2>/dev/null || echo -n 'Unknown'
echo "\","

echo -n "  \"availableMemory\": \""
if [ -f /proc/meminfo ]; then
    awk '/MemAvailable/ {printf "%.1fMB", $2/1024}' /proc/meminfo
else
    echo -n 'Unknown'
fi 2>/dev/null || echo -n 'Unknown'
echo "\","

# 5. 获取NPU设备数量（基于npu-smi info -m命令统计NPU ID数量）
echo -n "  \"npuDeviceCount\": \""
if command -v npu-smi >/dev/null 2>&1; then
    # 使用npu-smi info -m获取设备列表，统计唯一的NPU ID数量
    npu-smi info -m 2>/dev/null | awk '
    BEGIN { started = 0 }
    /NPU.*ID.*Chip.*ID/ { started = 1; next }
    started && /^[[:space:]]*[0-9]+/ {
        print $1  # 输出NPU ID
    }' | sort -u | wc -l | tr -d '\n'
elif [ -d /sys/class/accel ]; then
    # 通过系统文件检测NPU设备
    ls /sys/class/accel/ 2>/dev/null | wc -l | tr -d '\n'
else
    echo -n '0'
fi 2>/dev/null || echo -n '0'
echo "\","

# 6. 获取NPU芯片总数（基于npu-smi info -m命令）
echo -n "  \"npuChipCount\": \""
if command -v npu-smi >/dev/null 2>&1; then
    # 使用npu-smi info -m获取芯片信息，格式: NPU ID Chip ID Chip Logic ID Chip Name
    npu-smi info -m 2>/dev/null | awk '
    BEGIN { count = 0; started = 0 }
    /NPU.*ID.*Chip.*ID/ { started = 1; next }
    started && /^[[:space:]]*[0-9]+/ { count++ }
    END { print count }' | tr -d '\n'
else
    echo -n '0'
fi 2>/dev/null || echo -n '0'
echo "\","

echo -n "  \"diskTotal\": \""
lsblk -d -b | grep 'disk' | awk '{sum += $4} END {printf "%.0f", sum / 1024 / 1024}'
echo "MB\","

# 19. 添加时间戳
echo "  \"timestamp\": \"$(date '+%Y-%m-%d %H:%M:%S')\","

# 20. 添加检测状态和NPU工具可用性
echo -n "  \"npuSmiAvailable\": "
if command -v npu-smi >/dev/null 2>&1; then
    echo -n "true"
else
    echo -n "false"
fi
echo ","

echo "  \"success\": true"

echo "}"