services:
            remotetask:
    image:  quay.io/jupyter/base-notebook
    volumes:
      - /usr/local/dcmi:/usr/local/dcmi
      - /usr/local/bin/npu-smi:/usr/local/bin/npu-smi
      - /usr/local/sbin/npu-smi:/usr/local/sbin/npu-smi
      - /usr/local/Ascend/driver:/usr/local/Ascend/driver
      - /usr/local/Ascend/firmware:/usr/local/Ascend/firmware
      - /usr/share/zoneinfo/Asia/Shanghai:/etc/localtime:ro
      - /serverPath/user:/containerPath/user
    ports:
      - 42751:8888
    command: "start-notebook.py --NotebookApp.token='b17f24ff026d40949c85a24f4f375d42' "
    networks:
      code-network:
        aliases:
          - code-1935708225999474688
networks:
  code-network:
    external: true
