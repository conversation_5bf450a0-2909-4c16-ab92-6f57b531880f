#!/bin/bash

# 快速修复Nginx配置问题的脚本
# 专门针对 "host not found in upstream" 错误

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_warn() { echo -e "${YELLOW}[WARN]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }

# 配置参数
NGINX_CONTAINER="code_proxy_nginx"
NGINX_CONFIG_PATH="/etc/nginx/nginx.conf"

# 从错误信息中提取容器名称
extract_container_name() {
    local error_msg="$1"
    echo "$error_msg" | grep -o 'code-[0-9]\+' | head -1
}

# 检查容器是否存在并运行
check_container_status() {
    local container_name="$1"
    
    if docker ps --filter name="$container_name" --format "{{.Names}}" | grep -q "^$container_name$"; then
        log_success "容器 $container_name 正在运行"
        return 0
    elif docker ps -a --filter name="$container_name" --format "{{.Names}}" | grep -q "^$container_name$"; then
        log_warn "容器 $container_name 存在但未运行"
        return 1
    else
        log_error "容器 $container_name 不存在"
        return 2
    fi
}

# 尝试启动容器
start_container() {
    local container_name="$1"
    
    log_info "尝试启动容器 $container_name..."
    
    if docker start "$container_name" >/dev/null 2>&1; then
        sleep 3
        if docker ps --filter name="$container_name" --format "{{.Names}}" | grep -q "^$container_name$"; then
            log_success "容器 $container_name 启动成功"
            return 0
        else
            log_error "容器 $container_name 启动失败"
            return 1
        fi
    else
        log_error "无法启动容器 $container_name"
        return 1
    fi
}

# 从Nginx配置中移除无效的location块
remove_invalid_location() {
    local container_name="$1"
    local config_backup="/tmp/nginx.conf.backup.$(date +%s)"
    
    log_info "备份当前Nginx配置到 $config_backup"
    docker exec "$NGINX_CONTAINER" cp "$NGINX_CONFIG_PATH" "$config_backup"
    
    log_info "从Nginx配置中移除 $container_name 的location块..."
    
    # 创建临时脚本来移除location块
    local temp_script="/tmp/remove_location.sh"
    cat > "$temp_script" << 'EOF'
#!/bin/bash
CONTAINER_NAME="$1"
CONFIG_FILE="$2"

# 使用awk移除指定的location块
awk -v container="$CONTAINER_NAME" '
BEGIN { in_location = 0; brace_count = 0 }
{
    if ($0 ~ "location.*" container "/") {
        in_location = 1
        brace_count = 0
        next
    }
    
    if (in_location) {
        for (i = 1; i <= length($0); i++) {
            char = substr($0, i, 1)
            if (char == "{") brace_count++
            if (char == "}") brace_count--
        }
        
        if (brace_count <= 0 && $0 ~ "}") {
            in_location = 0
            next
        }
        next
    }
    
    print $0
}' "$CONFIG_FILE" > "$CONFIG_FILE.tmp" && mv "$CONFIG_FILE.tmp" "$CONFIG_FILE"
EOF

    chmod +x "$temp_script"
    
    # 在容器内执行移除操作
    docker cp "$temp_script" "$NGINX_CONTAINER:/tmp/"
    docker exec "$NGINX_CONTAINER" /tmp/remove_location.sh "$container_name" "$NGINX_CONFIG_PATH"
    
    # 清理临时文件
    docker exec "$NGINX_CONTAINER" rm -f /tmp/remove_location.sh
    rm -f "$temp_script"
    
    log_success "已从配置中移除 $container_name 的location块"
}

# 验证并重新加载Nginx配置
reload_nginx() {
    log_info "验证Nginx配置语法..."
    
    if docker exec "$NGINX_CONTAINER" nginx -t >/dev/null 2>&1; then
        log_success "Nginx配置语法正确"
        
        log_info "重新加载Nginx配置..."
        if docker exec "$NGINX_CONTAINER" nginx -s reload >/dev/null 2>&1; then
            log_success "Nginx配置重新加载成功"
            return 0
        else
            log_error "Nginx配置重新加载失败"
            return 1
        fi
    else
        log_error "Nginx配置语法错误"
        docker exec "$NGINX_CONTAINER" nginx -t
        return 1
    fi
}

# 主修复函数
fix_nginx_error() {
    local error_msg="$1"
    
    if [ -z "$error_msg" ]; then
        log_error "请提供错误信息"
        echo "用法: $0 'nginx: [emerg] host not found in upstream \"code-1935803844944465920\"'"
        exit 1
    fi
    
    log_info "分析错误信息: $error_msg"
    
    # 提取容器名称
    local container_name=$(extract_container_name "$error_msg")
    
    if [ -z "$container_name" ]; then
        log_error "无法从错误信息中提取容器名称"
        exit 1
    fi
    
    log_info "检测到问题容器: $container_name"
    
    # 检查容器状态
    check_container_status "$container_name"
    local status=$?
    
    case $status in
        0)
            log_info "容器正在运行，可能是网络问题"
            log_info "尝试重新加载Nginx配置..."
            reload_nginx
            ;;
        1)
            log_info "尝试启动容器..."
            if start_container "$container_name"; then
                reload_nginx
            else
                log_warn "无法启动容器，将从配置中移除"
                remove_invalid_location "$container_name"
                reload_nginx
            fi
            ;;
        2)
            log_info "容器不存在，从配置中移除相关配置"
            remove_invalid_location "$container_name"
            reload_nginx
            ;;
    esac
}

# 显示使用帮助
show_help() {
    echo "Nginx配置快速修复工具"
    echo ""
    echo "用法:"
    echo "  $0 'error_message'    修复指定的错误"
    echo "  $0 --auto            自动检测并修复所有问题"
    echo "  $0 --help            显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 'nginx: [emerg] host not found in upstream \"code-1935803844944465920\"'"
    echo ""
}

# 自动修复模式
auto_fix() {
    log_info "启动自动修复模式..."
    
    # 检查Nginx容器状态
    if ! docker ps --filter name="$NGINX_CONTAINER" --format "{{.Names}}" | grep -q "$NGINX_CONTAINER"; then
        log_error "Nginx容器 $NGINX_CONTAINER 未运行"
        exit 1
    fi
    
    # 获取Nginx错误日志
    local error_log=$(docker exec "$NGINX_CONTAINER" tail -n 100 /var/log/nginx/error.log 2>/dev/null || echo "")
    
    if [ -z "$error_log" ]; then
        log_warn "无法读取Nginx错误日志"
        return
    fi
    
    # 查找 "host not found" 错误
    local errors=$(echo "$error_log" | grep "host not found in upstream" | tail -5)
    
    if [ -z "$errors" ]; then
        log_info "未发现 'host not found' 错误"
        return
    fi
    
    log_info "发现以下错误:"
    echo "$errors"
    echo ""
    
    # 处理每个错误
    echo "$errors" | while read -r error_line; do
        if [ -n "$error_line" ]; then
            log_info "处理错误: $error_line"
            fix_nginx_error "$error_line"
            echo "----------------------------------------"
        fi
    done
}

# 主程序
main() {
    case "${1:-}" in
        --help|-h)
            show_help
            ;;
        --auto|-a)
            auto_fix
            ;;
        "")
            show_help
            exit 1
            ;;
        *)
            fix_nginx_error "$1"
            ;;
    esac
}

# 运行主程序
main "$@"
