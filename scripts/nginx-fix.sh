#!/bin/bash

# Nginx配置修复脚本
# 用于诊断和修复Nginx配置中的容器主机名解析问题

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

# 检查Docker是否运行
check_docker() {
    log_info "检查Docker服务状态..."
    if ! docker info >/dev/null 2>&1; then
        log_error "Docker服务未运行或无法访问"
        exit 1
    fi
    log_success "Docker服务正常"
}

# 检查Nginx容器状态
check_nginx_container() {
    log_info "检查Nginx代理容器状态..."
    
    if ! docker ps --filter name=code_proxy_nginx --format "{{.Names}}" | grep -q code_proxy_nginx; then
        log_error "Nginx代理容器 'code_proxy_nginx' 未运行"
        
        # 检查容器是否存在但已停止
        if docker ps -a --filter name=code_proxy_nginx --format "{{.Names}}" | grep -q code_proxy_nginx; then
            log_warn "容器存在但已停止，尝试启动..."
            docker start code_proxy_nginx
            sleep 2
            if docker ps --filter name=code_proxy_nginx --format "{{.Names}}" | grep -q code_proxy_nginx; then
                log_success "Nginx容器已启动"
            else
                log_error "无法启动Nginx容器"
                exit 1
            fi
        else
            log_error "Nginx容器不存在"
            exit 1
        fi
    else
        log_success "Nginx代理容器正常运行"
    fi
}

# 获取运行中的code容器
get_running_containers() {
    log_info "获取运行中的code容器..."
    
    local containers=$(docker ps --filter name=code- --format "{{.Names}}" | sort)
    
    if [ -z "$containers" ]; then
        log_warn "没有找到运行中的code容器"
        return 1
    fi
    
    log_info "找到以下运行中的容器:"
    echo "$containers" | while read container; do
        echo "  - $container"
    done
    
    echo "$containers"
}

# 检查容器网络连接
check_container_network() {
    local container_name=$1
    log_info "检查容器 $container_name 的网络连接..."
    
    # 获取容器的网络信息
    local networks=$(docker inspect "$container_name" --format '{{range $k, $v := .NetworkSettings.Networks}}{{$k}} {{end}}' 2>/dev/null || echo "")
    
    if [ -z "$networks" ]; then
        log_error "无法获取容器 $container_name 的网络信息"
        return 1
    fi
    
    log_info "容器 $container_name 连接的网络: $networks"
    
    # 检查是否在同一网络中
    local nginx_networks=$(docker inspect code_proxy_nginx --format '{{range $k, $v := .NetworkSettings.Networks}}{{$k}} {{end}}' 2>/dev/null || echo "")
    
    local common_network=""
    for net in $networks; do
        if echo "$nginx_networks" | grep -q "$net"; then
            common_network="$net"
            break
        fi
    done
    
    if [ -n "$common_network" ]; then
        log_success "容器 $container_name 与Nginx在同一网络 '$common_network' 中"
        return 0
    else
        log_error "容器 $container_name 与Nginx不在同一网络中"
        log_info "Nginx网络: $nginx_networks"
        log_info "容器网络: $networks"
        return 1
    fi
}

# 测试容器连通性
test_container_connectivity() {
    local container_name=$1
    local port=${2:-8080}
    
    log_info "测试从Nginx容器到 $container_name:$port 的连通性..."
    
    # 从Nginx容器内部测试连接
    if docker exec code_proxy_nginx sh -c "nc -z $container_name $port" >/dev/null 2>&1; then
        log_success "连接 $container_name:$port 成功"
        return 0
    else
        log_error "无法连接到 $container_name:$port"
        
        # 尝试ping测试
        if docker exec code_proxy_nginx sh -c "ping -c 1 $container_name" >/dev/null 2>&1; then
            log_warn "可以ping通 $container_name，但端口 $port 不可达"
        else
            log_error "无法ping通 $container_name"
        fi
        return 1
    fi
}

# 检查Nginx配置语法
check_nginx_syntax() {
    log_info "检查Nginx配置语法..."
    
    local result=$(docker exec code_proxy_nginx nginx -t 2>&1)
    
    if echo "$result" | grep -q "syntax is ok"; then
        log_success "Nginx配置语法正确"
        return 0
    else
        log_error "Nginx配置语法错误:"
        echo "$result"
        return 1
    fi
}

# 重新加载Nginx配置
reload_nginx() {
    log_info "重新加载Nginx配置..."
    
    if docker exec code_proxy_nginx nginx -s reload; then
        log_success "Nginx配置重新加载成功"
        return 0
    else
        log_error "Nginx配置重新加载失败"
        return 1
    fi
}

# 显示Nginx错误日志
show_nginx_logs() {
    log_info "显示Nginx错误日志 (最近50行)..."
    docker exec code_proxy_nginx tail -n 50 /var/log/nginx/error.log 2>/dev/null || log_warn "无法读取错误日志"
}

# 主函数
main() {
    echo "=========================================="
    echo "         Nginx配置修复工具"
    echo "=========================================="
    
    # 基础检查
    check_docker
    check_nginx_container
    
    # 获取运行中的容器
    local containers=$(get_running_containers)
    if [ $? -ne 0 ]; then
        log_warn "没有运行中的code容器，跳过连通性测试"
    else
        # 检查每个容器的网络和连通性
        echo "$containers" | while read container; do
            if [ -n "$container" ]; then
                echo "----------------------------------------"
                check_container_network "$container"
                test_container_connectivity "$container"
            fi
        done
    fi
    
    echo "----------------------------------------"
    
    # 检查Nginx配置
    if check_nginx_syntax; then
        reload_nginx
    else
        log_error "由于配置语法错误，跳过重新加载"
        show_nginx_logs
    fi
    
    echo "=========================================="
    log_info "诊断完成"
    
    # 提供修复建议
    echo ""
    log_info "修复建议:"
    echo "1. 确保容器名称与Nginx配置中的upstream主机名一致"
    echo "2. 确保所有容器都在同一Docker网络中"
    echo "3. 检查容器端口配置是否正确"
    echo "4. 使用API接口 /admin/app/schTaskInfo/fixNginxConfig 自动修复"
}

# 运行主函数
main "$@"
