# Nginx配置问题排查和解决方案

## 问题描述

错误信息：
```
nginx: [emerg] host not found in upstream "code-1935803844944465920" in /etc/nginx/nginx.conf:117
```

这个错误表示Nginx无法解析upstream中的主机名 `code-1935803844944465920`。

## 问题原因分析

1. **容器不存在或未运行**：目标容器 `code-1935803844944465920` 可能没有启动或已经停止
2. **网络配置问题**：容器与Nginx不在同一Docker网络中
3. **容器名称不匹配**：实际容器名称与Nginx配置中的不一致
4. **DNS解析问题**：Docker内部DNS无法解析容器名称

## 解决方案

### 方案1：使用自动修复API（推荐）

调用新增的API接口进行自动诊断和修复：

```bash
curl -X POST "http://your-server/admin/app/schTaskInfo/fixNginxConfig" \
  -H "Content-Type: application/json" \
  -d "资源ID"
```

该API会自动：
- 诊断配置问题
- 清理无效的location块
- 验证容器状态
- 重新加载Nginx配置

### 方案2：使用诊断脚本

运行提供的诊断脚本：

```bash
chmod +x scripts/nginx-fix.sh
./scripts/nginx-fix.sh
```

### 方案3：手动排查和修复

#### 步骤1：检查容器状态

```bash
# 检查目标容器是否运行
docker ps --filter name=code-1935803844944465920

# 检查所有code容器
docker ps --filter name=code-

# 如果容器存在但未运行，启动它
docker start code-1935803844944465920
```

#### 步骤2：检查网络配置

```bash
# 检查Nginx容器的网络
docker inspect code_proxy_nginx --format '{{range $k, $v := .NetworkSettings.Networks}}{{$k}} {{end}}'

# 检查目标容器的网络
docker inspect code-1935803844944465920 --format '{{range $k, $v := .NetworkSettings.Networks}}{{$k}} {{end}}'

# 如果不在同一网络，将容器加入Nginx网络
docker network connect <nginx_network> code-1935803844944465920
```

#### 步骤3：测试连通性

```bash
# 从Nginx容器测试连接
docker exec code_proxy_nginx ping code-1935803844944465920
docker exec code_proxy_nginx nc -z code-1935803844944465920 8080
```

#### 步骤4：清理无效配置

如果容器确实不存在，需要从Nginx配置中移除对应的location块：

1. 编辑Nginx配置文件
2. 删除包含 `code-1935803844944465920` 的location块
3. 验证配置语法：`docker exec code_proxy_nginx nginx -t`
4. 重新加载配置：`docker exec code_proxy_nginx nginx -s reload`

## 预防措施

### 1. 改进容器生命周期管理

在 `SchTaskInfoServiceImpl.java` 中添加容器状态验证：

```java
// 在生成Nginx配置前验证容器状态
if (!verifyContainerExists(connection, containerNetworkName)) {
    throw new MyRuntimeException("容器不存在或未运行");
}
```

### 2. 增强错误处理

```java
// 添加配置语法验证
String testResult = sshConnectionUtil.executeCommand(connection, "docker exec code_proxy_nginx nginx -t");
if (!testResult.contains("syntax is ok")) {
    throw new MyRuntimeException("Nginx配置语法错误");
}
```

### 3. 定期清理配置

实现定期任务清理无效的location块：

```java
@Scheduled(fixedRate = 300000) // 每5分钟执行一次
public void cleanupNginxConfig() {
    // 获取运行中的容器列表
    // 清理无效的location块
    // 重新加载配置
}
```

## 代码改进

### 1. 增强的Nginx配置生成

```java
public String buildNginxLocationConfig(String codePrefix, String netWorkName, String port) {
    log.info("Building Nginx location config - codePrefix: {}, netWorkName: {}, port: {}", 
            codePrefix, netWorkName, port);
    
    return String.format(
            """
            location ~ ^/%s/ {
                proxy_pass http://%s:%s;
                rewrite ^/%s/(.*)$ /$1 break;
                access_log /var/log/nginx/%s_access.log main;
                error_log /var/log/nginx/%s_error.log debug;
            }
            """,
            codePrefix, netWorkName, port, codePrefix, codePrefix, codePrefix
    );
}
```

### 2. 容器状态验证

```java
private boolean verifyContainerExists(SshConnection connection, String containerName) {
    try {
        String checkCmd = String.format("docker ps --filter name=%s --format '{{.Names}}'", containerName);
        String result = sshConnectionUtil.executeCommand(connection, checkCmd);
        return result != null && result.trim().contains(containerName);
    } catch (Exception e) {
        log.error("Error verifying container existence: {}", e.getMessage());
        return false;
    }
}
```

## 监控和告警

### 1. 添加健康检查

```java
@GetMapping("/nginx/health")
public ResponseResult<Map<String, Object>> checkNginxHealth() {
    Map<String, Object> health = new HashMap<>();
    
    // 检查Nginx容器状态
    // 检查配置语法
    // 检查upstream连通性
    
    return ResponseResult.success(health);
}
```

### 2. 日志监控

监控Nginx错误日志中的 "host not found" 错误，及时发现问题。

## 总结

这个问题主要是由于容器生命周期管理不当导致的。通过：

1. 增强容器状态验证
2. 改进错误处理
3. 实现自动清理机制
4. 添加监控告警

可以有效预防和快速解决此类问题。

建议优先使用自动修复API，它会自动诊断问题并执行相应的修复操作。
